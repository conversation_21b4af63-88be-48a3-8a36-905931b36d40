# 任务模板 (Task Template)

**任务ID**: task_YYYYMMDD_HHMMSS_[简短任务描述]  
**创建时间**: [自动生成]  
**状态**: 规划中  
**优先级**: [高/中/低]  

## 🎯 任务目标

**业务目标**: [描述要达成的业务价值]  
**技术目标**: [描述要实现的技术功能]  
**成功标准**: [明确的、可验证的完成标准]  

## 📋 需求分析

### 用户故事
作为 [角色]，我希望 [功能]，以便 [价值]。

### 验收标准
- [ ] 当 [条件] 时，系统应该 [行为]
- [ ] 当 [条件] 时，系统应该 [行为]
- [ ] 当 [条件] 时，系统应该 [行为]

## 🔍 技术分析

### 涉及的文件/组件
- [ ] `路径/文件名` - [修改类型: 新建/修改/删除]
- [ ] `路径/文件名` - [修改类型: 新建/修改/删除]

### 依赖的规则
- [ ] [规则ID] - [规则名称]
- [ ] [规则ID] - [规则名称]

### 技术风险
- [ ] [风险描述] - [影响程度] - [缓解方案]

## 📝 执行计划

### 步骤 1: [步骤名称]
**目标**: [此步骤要达成的目标]  
**操作**: [具体的操作内容]  
**验证**: [如何验证此步骤完成]  
**状态**: [ ] 待执行 / [x] 已完成  

### 步骤 2: [步骤名称]
**目标**: [此步骤要达成的目标]  
**操作**: [具体的操作内容]  
**验证**: [如何验证此步骤完成]  
**状态**: [ ] 待执行 / [x] 已完成  

### 步骤 3: [步骤名称]
**目标**: [此步骤要达成的目标]  
**操作**: [具体的操作内容]  
**验证**: [如何验证此步骤完成]  
**状态**: [ ] 待执行 / [x] 已完成  

## 🧪 测试计划

### 单元测试
- [ ] [测试用例描述]
- [ ] [测试用例描述]

### 集成测试
- [ ] [测试场景描述]
- [ ] [测试场景描述]

### 手动测试
- [ ] [测试步骤描述]
- [ ] [测试步骤描述]

## 📊 进度跟踪

| 阶段 | 开始时间 | 完成时间 | 状态 | 备注 |
|------|----------|----------|------|------|
| 需求分析 | | | ⏳ | |
| 技术设计 | | | ⏳ | |
| 代码实现 | | | ⏳ | |
| 测试验证 | | | ⏳ | |
| 文档更新 | | | ⏳ | |

## 🔄 变更记录

| 时间 | 变更内容 | 原因 |
|------|----------|------|
| [时间] | [变更描述] | [变更原因] |

## 🎓 经验总结

### 遇到的问题
- [问题描述] - [解决方案]

### 学到的经验
- [经验描述]

### 可优化的地方
- [优化建议]

## 📚 相关资源

- [相关文档链接]
- [参考资料链接]
- [相关任务链接]

---

**模板使用说明**:
1. 复制此模板到 `.docs/TEMP/` 目录
2. 重命名为具体的任务文件名
3. 填写所有必要信息
4. 在执行过程中及时更新状态
5. 任务完成后进行经验总结
