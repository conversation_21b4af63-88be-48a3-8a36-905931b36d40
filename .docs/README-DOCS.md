# 项目文档框架 (Project Documentation Framework)

## 📋 项目概览

**项目名称**: study2  
**项目类型**: 商业应用 (Uni-app 跨平台应用)  
**技术栈**: uniapp + Vue3 + TypeScript + Vite5 + UnoCSS + wot-ui + z-paging  
**开发阶段**: 功能开发  
**团队规模**: 个人项目  
**核心关注**: 代码质量 + 开发效率  

## 🧠 共享大脑结构

`.docs/` 目录是我们的"共享大脑"，包含以下核心部分：

### 📊 STATE/ - 事实基线
存放描述项目当前状态的"事实基线"文档，是所有变更的出发点。

- `PROJECT-OVERVIEW.md` - 项目整体状况
- `TECH-STACK.md` - 技术栈现状
- `API-ENDPOINTS.md` - API 接口现状
- `COMPONENT-REGISTRY.md` - 组件注册表
- `DEPENDENCY-MAP.md` - 依赖关系图

### 🔄 PROCESS/ - 过程流水
存放用于推动状态变更的"过程流水"文档。

- `TEMPLATES/` - 标准化模板
- `PROPOSALS/` - 重大变更提案
- `WORKFLOWS/` - 工作流程定义

### 📏 RULES/ - 开发规则
存放从过往经验中沉淀的、必须遵守的"开发规则"，拥有最高优先级。

- `LR-001_CODE_QUALITY.md` - 代码质量规则
- `LR-002_COMPONENT_DESIGN.md` - 组件设计规则
- `LR-003_API_INTEGRATION.md` - API 集成规则

### 🗂️ TEMP/ - 临时状态
存放任务执行过程中的状态文件，确保任务状态的一致性。

## 🎯 核心原则

1. **依据驱动**: 每个决策都有明确的事实依据
2. **基线先行**: 所有变更基于当前状态基线
3. **状态外化**: 复杂任务状态持久化到 TEMP/ 目录
4. **测试驱动**: 逻辑相关编码先定义测试用例
5. **模板驱动**: 使用标准化模板创建文档
6. **经验学习**: 从每次任务中提炼可复用规则
7. **用户意图至上**: 明确指令拥有最高优先级

## 🚀 快速开始

1. **查看项目状态**: 阅读 `STATE/` 目录下的基线文档
2. **了解开发规则**: 查阅 `RULES/` 目录下的规则文档
3. **使用标准流程**: 参考 `PROCESS/TEMPLATES/` 下的模板
4. **跟踪任务状态**: 复杂任务会在 `TEMP/` 目录创建状态文件

## 📝 文档维护

- **状态更新**: 代码变更后及时更新 STATE/ 文档
- **规则沉淀**: 从经验中提炼新规则到 RULES/ 目录
- **模板优化**: 根据使用情况优化 TEMPLATES/ 模板
- **临时清理**: 定期清理 TEMP/ 目录中的过期文件

---

*最后更新: 2025-01-27*  
*版本: 1.0.0*
