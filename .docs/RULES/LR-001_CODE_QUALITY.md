# 代码质量规则

**规则ID**: LR-001  
**规则名称**: 代码质量保障规则  
**状态**: Active  
**创建时间**: 2025-01-27  
**适用范围**: 全项目  

## 🎯 规则概述

确保项目代码质量，提升代码可维护性和开发效率。

## 📋 核心规则

### 1. TypeScript 严格模式规则

**触发情景**: 编写任何 `.ts` 或 `.vue` 文件时

**指令 (MUST)**:
- 必须启用 TypeScript 严格模式
- 必须为所有函数参数和返回值定义类型
- 必须为组件 props 定义明确的类型接口
- 禁止使用 `any` 类型，除非有明确的业务理由

**示例**:
```typescript
// ✅ 正确
interface UserInfo {
  id: number
  name: string
  email?: string
}

function getUserInfo(userId: number): Promise<UserInfo> {
  // 实现
}

// ❌ 错误
function getUserInfo(userId: any): any {
  // 实现
}
```

### 2. 组件开发规则

**触发情景**: 创建或修改 Vue 组件时

**指令 (MUST)**:
- 组件必须使用 `<script setup lang="ts">` 语法
- 组件 props 必须使用 `defineProps<T>()` 定义类型
- 组件必须有明确的命名规范 (PascalCase)
- 业务组件放在 `src/components/business/`
- 通用组件放在 `src/components/common/`

**示例**:
```vue
<!-- ✅ 正确的组件结构 -->
<script setup lang="ts">
interface Props {
  title: string
  count?: number
}

const props = defineProps<Props>()
const emit = defineEmits<{
  click: [value: string]
}>()
</script>

<template>
  <div>{{ props.title }}</div>
</template>
```

### 3. API 集成规则

**触发情景**: 集成后端 API 接口时

**指令 (MUST)**:
- 必须使用 `openapi-ts-request` 自动生成 API 代码
- 禁止手写 API 接口代码
- 必须在 `openapi-ts-request.config.ts` 中配置接口文档地址
- API 调用必须包含错误处理

**执行流程**:
1. 配置 `openapi-ts-request.config.ts` 中的 `schemaPath`
2. 运行 `pnpm run openapi-ts-request` 生成代码
3. 使用生成的 API 函数进行接口调用

### 4. 文件命名规则

**触发情景**: 创建任何新文件时

**指令 (MUST)**:
- Vue 组件: PascalCase (如 `UserProfile.vue`)
- 工具函数: camelCase (如 `formatDate.ts`)
- 类型定义: PascalCase (如 `UserTypes.ts`)
- 页面文件: kebab-case (如 `user-profile.vue`)
- 目录名: kebab-case (如 `user-management/`)

### 5. 导入路径规则

**触发情景**: 在文件中导入其他模块时

**指令 (MUST)**:
- 必须使用 `@/` 别名引用 src 目录
- 必须使用 `@img/` 别名引用静态图片
- 相对路径仅用于同目录或子目录文件
- 第三方库导入放在文件顶部

**示例**:
```typescript
// ✅ 正确
import { ref } from 'vue'
import type { UserInfo } from '@/types/user'
import { formatDate } from '@/utils/date'
import logo from '@img/logo.png'

// ❌ 错误
import { formatDate } from '../../../utils/date'
```

## 🔍 质量检查工具

### 自动化检查
- **ESLint**: 代码风格和潜在错误检查
- **TypeScript**: 类型检查
- **Commitlint**: 提交信息规范检查

### 手动检查清单
- [ ] 组件是否有明确的类型定义
- [ ] 是否遵循命名规范
- [ ] 是否有适当的错误处理
- [ ] 是否使用了正确的导入路径

## 🚨 违规处理

**轻微违规**: 开发时 ESLint 警告，必须修复后提交
**严重违规**: 构建失败，阻止部署

## 📚 参考资源

- [Vue 3 官方文档](https://vuejs.org/)
- [TypeScript 官方文档](https://www.typescriptlang.org/)
- [ESLint 规则文档](https://eslint.org/docs/rules/)
- [Unibest 开发规范](./DEVELOPMENT_RULES.md)

## 🔄 规则更新

当发现新的代码质量问题或最佳实践时，应及时更新此规则文档。

---

*此规则基于 unibest 最佳实践和商业应用开发经验制定*
