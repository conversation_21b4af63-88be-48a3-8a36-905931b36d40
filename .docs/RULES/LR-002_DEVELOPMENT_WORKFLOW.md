# 开发工作流规则

**规则ID**: LR-002  
**规则名称**: 开发工作流规则  
**状态**: Active  
**创建时间**: 2025-01-27  
**适用范围**: 全项目开发流程  

## 🎯 规则概述

规范开发工作流程，确保高效且高质量的代码交付。

## 📋 核心工作流规则

### 1. 任务启动规则

**触发情景**: 开始任何非平凡的开发任务时

**指令 (MUST)**:
1. 必须先查阅 `.docs/RULES/` 下的相关规则
2. 必须在 `.docs/TEMP/` 创建任务状态文件
3. 必须使用 `.docs/PROCESS/TEMPLATES/TASK-TEMPLATE.md` 模板
4. 必须明确定义成功标准

**任务分类**:
- **A级 (零仪式)**: 错字修正、注释调整 → 直接执行
- **B级 (快速通道)**: 小功能、简单修改 → 简化流程
- **C级 (标准通道)**: 常规开发任务 → 完整流程
- **D级 (提案驱动)**: 重大变更 → 需要提案和批准

### 2. 代码实现规则

**触发情景**: 编写或修改代码时

**指令 (MUST)**:
1. 必须遵循 `LR-001_CODE_QUALITY.md` 中的代码质量规则
2. 必须使用 TypeScript 严格模式
3. 必须为组件定义明确的 Props 接口
4. 必须使用约定式路由 (uni-pages)
5. 必须使用自动组件注册

**代码结构要求**:
```typescript
// ✅ 标准组件结构
<script setup lang="ts">
interface Props {
  // 明确的 Props 定义
}

interface Emits {
  // 明确的事件定义
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()
</script>
```

### 3. API 集成规则

**触发情景**: 集成后端 API 时

**指令 (MUST)**:
1. 必须使用 `openapi-ts-request` 自动生成 API 代码
2. 禁止手写 API 接口函数
3. 必须在 `openapi-ts-request.config.ts` 中配置接口文档地址
4. 必须处理 API 调用的错误情况

**执行步骤**:
```bash
# 1. 配置接口文档地址
# 编辑 openapi-ts-request.config.ts

# 2. 生成 API 代码
pnpm run openapi-ts-request

# 3. 使用生成的 API 函数
import { useUserApi } from '@/service/app'
```

### 4. 状态管理规则

**触发情景**: 需要跨组件状态共享时

**指令 (SHOULD)**:
- 简单状态: 使用 Vue 3 组合式 API
- 复杂状态: 使用 Pinia store
- 临时状态: 使用 ref/reactive
- 持久化状态: 使用 Pinia + 持久化插件

**Pinia Store 结构**:
```typescript
// stores/user.ts
export const useUserStore = defineStore('user', () => {
  const userInfo = ref<UserInfo | null>(null)
  
  const login = async (credentials: LoginCredentials) => {
    // 登录逻辑
  }
  
  return {
    userInfo,
    login
  }
})
```

### 5. 样式开发规则

**触发情景**: 编写组件样式时

**指令 (MUST)**:
1. 优先使用 UnoCSS 原子化类名
2. 复杂样式使用 CSS 变量
3. 响应式设计使用 UnoCSS 断点
4. 主题色使用 wot-design-uni 的设计令牌

**样式优先级**:
```vue
<template>
  <!-- 1. 优先使用 UnoCSS -->
  <div class="flex items-center justify-center p-4 bg-primary text-white">
    
  <!-- 2. 复杂样式使用 CSS 类 -->
  <div class="custom-component">
    
  <!-- 3. 动态样式使用内联样式 -->
  <div :style="{ color: dynamicColor }">
</template>

<style scoped>
.custom-component {
  /* 复杂的自定义样式 */
}
</style>
```

### 6. 测试规则

**触发情景**: 完成功能开发后

**指令 (SHOULD)**:
1. 应该为工具函数编写单元测试
2. 应该为关键业务逻辑编写测试
3. 应该进行手动功能测试
4. 应该在多个平台验证功能

**测试清单**:
- [ ] 功能正常工作
- [ ] 错误情况处理正确
- [ ] 界面在不同设备上显示正常
- [ ] 性能表现可接受

### 7. 文档同步规则

**触发情景**: 完成代码实现后

**指令 (MUST)**:
1. 必须更新相关的 `.docs/STATE/` 文档
2. 必须记录任务执行过程中的问题和解决方案
3. 必须在任务状态文件中标记完成状态
4. 必须提炼可复用的经验到 `.docs/RULES/`

## 🔄 工作流程图

```
开始任务
    ↓
查阅相关规则 (.docs/RULES/)
    ↓
创建任务状态文件 (.docs/TEMP/)
    ↓
分析需求和技术方案
    ↓
编写代码 (遵循质量规则)
    ↓
测试验证
    ↓
更新文档 (.docs/STATE/)
    ↓
经验总结和规则提炼
    ↓
任务完成
```

## 🚨 质量门禁

### 代码提交前检查
- [ ] ESLint 检查通过
- [ ] TypeScript 编译通过
- [ ] 功能测试通过
- [ ] 相关文档已更新

### 任务完成前检查
- [ ] 任务状态文件已更新
- [ ] 成功标准已达成
- [ ] 经验已总结
- [ ] 规则已提炼 (如适用)

## 📚 相关资源

- [LR-001 代码质量规则](./LR-001_CODE_QUALITY.md)
- [任务模板](../PROCESS/TEMPLATES/TASK-TEMPLATE.md)
- [项目状态文档](../STATE/)

---

*此规则确保开发工作的一致性和高效性*
