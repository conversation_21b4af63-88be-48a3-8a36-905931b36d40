# Unibest 项目开发规范

## 1. 代码规范

### 1.1 命名规范

#### 文件命名
- **页面文件**: 使用 kebab-case，如 `user-profile.vue`
- **组件文件**: 使用 PascalCase，如 `UserCard.vue`
- **工具文件**: 使用 camelCase，如 `formatUtils.ts`
- **常量文件**: 使用 UPPER_SNAKE_CASE，如 `API_CONSTANTS.ts`

#### 变量命名
- **变量和函数**: 使用 camelCase，如 `userName`, `getUserInfo()`
- **常量**: 使用 UPPER_SNAKE_CASE，如 `MAX_RETRY_COUNT`
- **组件 props**: 使用 camelCase，如 `isVisible`, `maxLength`
- **CSS 类名**: 使用 kebab-case，如 `user-card`, `btn-primary`

### 1.2 目录结构规范

```
src/
├── components/          # 公共组件
│   ├── common/         # 通用组件
│   └── business/       # 业务组件
├── pages/              # 页面文件
├── pages-sub/          # 分包页面
├── static/             # 静态资源
│   ├── images/         # 图片资源
│   └── icons/          # 图标资源
├── stores/             # Pinia 状态管理
├── utils/              # 工具函数
├── hooks/              # 组合式函数
├── types/              # TypeScript 类型定义
├── service/            # API 服务
└── styles/             # 全局样式

# 根目录配置文件
├── pages.config.ts      # 页面配置（替代 pages.json）
├── manifest.config.ts   # 应用配置（替代 manifest.json）
├── vite.config.ts       # Vite 构建配置
└── uno.config.ts        # UnoCSS 配置
```

### 1.3 配置文件管理规范

#### ⚠️ 重要：禁止直接修改自动生成的文件

**禁止修改的文件：**
- `src/pages.json` - 由 `pages.config.ts` 自动生成
- `src/manifest.json` - 由 `manifest.config.ts` 自动生成

**原因：** 项目集成了 `@uni-helper/vite-plugin-uni-pages` 和 `@uni-helper/vite-plugin-uni-manifest` 插件，这些文件会在构建时自动生成，手动修改将被覆盖。

#### 正确的配置方式

**1. 页面配置 - 使用 `pages.config.ts`**
```typescript
// pages.config.ts
import { defineUniPages } from '@uni-helper/vite-plugin-uni-pages'

export default defineUniPages({
  // 全局样式配置
  globalStyle: {
    navigationStyle: 'default',
    navigationBarTitleText: 'unibest',
    navigationBarBackgroundColor: '#f8f8f8',
    navigationBarTextStyle: 'black',
    backgroundColor: '#FFFFFF',
  },
  // 组件自动导入配置
  easycom: {
    autoscan: true,
    custom: {
      '^fg-(.*)': '@/components/fg-$1/fg-$1.vue',
      '^wd-(.*)': 'wot-design-uni/components/wd-$1/wd-$1.vue',
    },
  },
  // TabBar 配置
  tabBar: {
    // TabBar 配置项
  },
})
```

**2. 页面路由配置 - 使用 Vue 文件的 route-block**
```vue
<!-- src/pages/user/profile.vue -->
<route lang="json">
{
  "style": {
    "navigationBarTitleText": "个人资料",
    "enablePullDownRefresh": true
  }
}
</route>

<template>
  <!-- 页面内容 -->
</template>
```

**3. 设置首页 - 在 Vue 文件中配置**
```vue
<!-- src/pages/home/<USER>
<route lang="json">
{
  "type": "home",
  "style": {
    "navigationBarTitleText": "首页"
  }
}
</route>
```

**注意：** 确保项目中只有一个页面设置 `"type": "home"`

**4. 分包配置 - 在 `vite.config.ts` 中配置**
```typescript
// vite.config.ts
UniPages({
  exclude: ['**/components/**/**.*'],
  subPackages: ['src/pages-sub'], // 分包目录
  dts: 'src/types/uni-pages.d.ts',
})
```

**5. 应用配置 - 使用 `manifest.config.ts`**
```typescript
// manifest.config.ts
import { defineManifestConfig } from '@uni-helper/vite-plugin-uni-manifest'

export default defineManifestConfig({
  name: '应用名称',
  appid: '__UNI__XXXXXXX',
  description: '应用描述',
  versionName: '1.0.0',
  versionCode: '100',
  // 小程序配置
  'mp-weixin': {
    appid: 'wxXXXXXXXXXX',
    setting: {
      urlCheck: false,
      es6: true,
      minified: true,
    },
  },
  // H5 配置
  h5: {
    router: {
      base: '/',
    },
  },
})
```

### 1.4 Vue 组件规范

#### 组件结构顺序
```vue
<template>
  <!-- 模板内容 -->
</template>

<script setup lang="ts">
// 1. 导入依赖
// 2. 定义 props
// 3. 定义 emits
// 4. 定义响应式数据
// 5. 定义计算属性
// 6. 定义方法
// 7. 生命周期钩子
</script>

<style lang="scss" scoped>
/* 样式内容 */
</style>
```

#### Props 定义规范
```typescript
interface Props {
  title: string
  isVisible?: boolean
  maxCount?: number
}

const props = withDefaults(defineProps<Props>(), {
  isVisible: true,
  maxCount: 10
})
```

## 2. TypeScript 规范

### 2.1 类型定义
- 所有 API 接口必须定义类型
- 组件 props 必须使用 TypeScript 接口
- 避免使用 `any` 类型，使用 `unknown` 替代
- 优先使用 `interface` 而不是 `type`

### 2.2 类型文件组织
```typescript
// types/api.ts - API 相关类型
export interface UserInfo {
  id: number
  name: string
  avatar?: string
}

// types/common.ts - 通用类型
export interface BaseResponse<T = any> {
  code: number
  data: T
  message: string
}
```

## 3. 样式规范

### 3.1 CSS 规范
- 使用 SCSS 预处理器
- 组件样式必须使用 `scoped`
- 使用 UnoCSS 原子化 CSS 优先
- 避免深层嵌套（最多 3 层）

### 3.2 响应式设计
```scss
// 使用 uni-app 的 rpx 单位
.container {
  width: 750rpx;
  padding: 20rpx;
}

// 媒体查询
@media (max-width: 768px) {
  .container {
    padding: 10rpx;
  }
}
```

## 4. API 规范

### 4.1 自动生成接口 API

#### 4.1.1 配置说明
项目集成了 [openapi-ts-request](https://github.com/openapi-ui/openapi-ts-request) 插件，支持根据接口文档自动生成代码。

**支持的接口文档格式：**
- Swagger/OpenAPI
- Apifox
- YApi
- 其他符合 OpenAPI 规范的文档

#### 4.1.2 配置文件
在根目录的 `openapi-ts-request.config.ts` 中配置：

```typescript
import type { GenerateServiceProps } from 'openapi-ts-request'

export default [
  {
    // 接口文档地址
    schemaPath: 'https://your-api-docs.com/swagger.json',
    // 生成代码存放目录
    serversPath: './src/service/app',
    // 请求库导入路径
    requestLibPath: `import request from '@/utils/request';\n import { CustomRequestOptions } from '@/http/interceptor';`,
    // 请求选项类型
    requestOptionsType: 'CustomRequestOptions',
    // 生成 Vue Query 代码
    isGenReactQuery: true,
    reactQueryMode: 'vue',
    // 不生成 JavaScript 代码
    isGenJavaScript: false,
  },
] as GenerateServiceProps[]
```

#### 4.1.3 执行流程规范

**步骤 1：配置接口文档地址**
1. 获取后端提供的接口文档 URL（支持 Swagger/OpenAPI/Apifox/YApi）
2. 在 `openapi-ts-request.config.ts` 中配置 `schemaPath` 字段
3. 可同时配置多个接口文档 URL

**步骤 2：执行生成命令**
```bash
# 生成接口代码（必须执行）
pnpm run openapi-ts-request

# 或使用 npm
npm run openapi-ts-request
```

**步骤 3：验证生成结果**
- 检查 `src/service/app/` 目录下是否生成了对应文件
- 确认类型文件 `types.ts` 是否正确生成
- 验证 Vue Query 文件（`.vuequery.ts`）是否存在

**执行时机规范：**
- 🔴 **必须执行**：接口文档更新后
- 🔴 **必须执行**：新增接口模块时  
- 🔴 **必须执行**：项目初始化时
- 🟡 **建议执行**：定期同步（每日/每周）
- ⚠️ **注意**：生成的代码不要手动修改

#### 4.1.4 生成的文件结构
```
src/service/app/
├── types.ts              # TypeScript 类型定义
├── moduleA.ts            # 模块 A 的 uni.request 代码
├── moduleA.vuequery.ts   # 模块 A 的 Vue Query 代码
├── moduleB.ts            # 模块 B 的 uni.request 代码
└── moduleB.vuequery.ts   # 模块 B 的 Vue Query 代码
```

#### 4.1.5 使用规范

**GET 请求使用 Vue Query：**
```typescript
import { useQuery } from '@tanstack/vue-query'
import { findPetsByStatusQueryOptions } from '@/service/app'

// 基础使用
const {
  data,
  error,
  isLoading,
  refetch,
} = useQuery(findPetsByStatusQueryOptions({ 
  params: { status: ['available'] } 
}))

// 带条件的查询
const {
  data,
  error,
  isLoading,
  refetch,
} = useQuery({
  ...findPetsByStatusQueryOptions({ 
    params: { status: ['available'] } 
  }),
  enabled: !!token, // 只有在有 token 时才执行查询
})
```

**POST/PUT/DELETE 请求使用 Mutation：**
```typescript
import { usePlaceOrderMutation } from '@/service/app'

// 定义 mutation
const { mutate, isPending } = usePlaceOrderMutation({
  onSuccess: (data) => {
    console.log('请求成功:', data)
    // 可以在这里更新缓存、跳转页面等
  },
  onError: (error) => {
    console.error('请求失败:', error)
    // 错误处理
  }
})

// 提交请求
const handleSubmit = () => {
  mutate({
    body: {
      status: 'placed',
      complete: true,
    }
  })
}
```

#### 4.1.6 开发流程规范

1. **接口文档优先**
   - 后端提供标准的 OpenAPI 文档
   - 前端根据文档生成接口代码
   - 避免手写接口调用代码

2. **代码生成规范**
   ```bash
   # 标准流程
   1. 配置接口文档 URL → openapi-ts-request.config.ts
   2. 执行生成命令 → pnpm run openapi-ts-request  
   3. 检查生成结果 → src/service/app/
   4. 在业务代码中使用 → import from '@/service/app'
   ```
   
   **重要规则：**
   - ✅ 接口文档更新后，**必须**重新执行生成命令
   - ✅ 团队成员拉取代码后，**必须**执行一次生成命令
   - ❌ **禁止**手动修改生成的代码文件
   - ✅ 如需自定义，在业务层进行封装

3. **命令执行检查清单**
   - [ ] 配置文件中的 `schemaPath` 是否正确
   - [ ] 执行 `pnpm run openapi-ts-request` 是否成功
   - [ ] `src/service/app/types.ts` 是否生成
   - [ ] 各模块的 `.vuequery.ts` 文件是否存在
   - [ ] 类型导入是否正常工作

4. **类型安全**
   - 使用生成的 TypeScript 类型
   - 避免使用 `any` 类型
   - 确保请求参数和响应数据的类型正确

### 4.2 手动接口定义（特殊情况）
当自动生成无法满足需求时，可以手动定义接口：

```typescript
// service/custom.ts
import { http } from '@/utils/http'

export interface CustomParams {
  id: string
  options?: Record<string, any>
}

export const customApi = {
  specialRequest: (params: CustomParams) => 
    http.post<ResponseType>('/custom/endpoint', params),
}
```

### 4.3 错误处理规范
- 统一错误处理机制
- 网络错误友好提示
- 接口异常日志记录
- 使用 Vue Query 的错误处理机制

### 4.4 缓存策略
- GET 请求使用 Vue Query 缓存
- 合理设置缓存时间
- 及时更新相关缓存数据

```typescript
// 设置缓存时间
const { data } = useQuery({
  ...getUserInfoQueryOptions({ params: { id: userId } }),
  staleTime: 5 * 60 * 1000, // 5分钟内数据不过期
  cacheTime: 10 * 60 * 1000, // 缓存保持10分钟
})
```

## 5. 状态管理规范

### 5.1 Pinia Store 结构
```typescript
// stores/user.ts
export const useUserStore = defineStore('user', () => {
  // state
  const userInfo = ref<UserInfo | null>(null)
  
  // getters
  const isLogin = computed(() => !!userInfo.value)
  
  // actions
  const login = async (params: LoginParams) => {
    const res = await userApi.login(params)
    userInfo.value = res.data
  }
  
  return {
    userInfo,
    isLogin,
    login
  }
})
```

## 6. 性能优化规范

### 6.1 图片优化
- 使用 WebP 格式图片
- 图片懒加载
- 合理的图片尺寸

### 6.2 代码分割
- 路由懒加载
- 组件按需导入
- 第三方库按需引入

## 7. 测试规范

### 7.1 单元测试
- 工具函数必须编写测试
- 复杂业务逻辑编写测试
- 测试覆盖率不低于 80%

## 8. Git 规范

### 8.1 提交信息格式
```
<type>(<scope>): <subject>

<body>

<footer>
```

#### Type 类型
- `feat`: 新功能
- `fix`: 修复 bug
- `docs`: 文档更新
- `style`: 代码格式调整
- `refactor`: 代码重构
- `test`: 测试相关
- `chore`: 构建过程或辅助工具的变动

#### 示例
```
feat(user): 添加用户登录功能

- 实现用户名密码登录
- 添加记住密码功能
- 集成第三方登录

Closes #123
```

### 8.2 分支管理
- `main`: 主分支，用于生产环境
- `develop`: 开发分支
- `feature/*`: 功能分支
- `hotfix/*`: 热修复分支

## 9. 环境配置规范

### 9.1 环境变量
- 开发环境: `.env.development`
- 生产环境: `.env.production`
- 测试环境: `.env.test`

### 9.2 配置文件
- 所有配置项必须有注释说明
- 敏感信息不得提交到代码库
- 使用 `VITE_` 前缀暴露给前端

## 10. 代码审查规范

### 10.1 审查要点
- 代码逻辑正确性
- 性能影响评估
- 安全性检查
- 代码规范遵循

### 10.2 审查流程
1. 自测通过后提交 PR
2. 至少一人审查通过
3. CI/CD 检查通过
4. 合并到目标分支

## 11. 文档规范

### 11.1 代码注释
```typescript
/**
 * 格式化用户信息
 * @param user 原始用户信息
 * @param options 格式化选项
 * @returns 格式化后的用户信息
 */
function formatUserInfo(user: UserInfo, options?: FormatOptions): FormattedUser {
  // 实现逻辑
}
```

### 11.2 README 文档
- 项目介绍
- 快速开始
- 开发指南
- 部署说明

## 12. 安全规范

### 12.1 数据安全
- 敏感数据加密存储
- 用户输入数据验证
- XSS 防护

### 12.2 接口安全
- Token 认证
- 接口权限控制
- 请求频率限制

## 13. 常见问题和注意事项

### 13.1 配置文件相关

#### 问题：修改 `pages.json`、`manifest.json` 被覆盖
**原因：** 项目使用了自动生成插件，手动修改会被覆盖。

**解决方案：**
- 全局配置在 `pages.config.ts` 中修改
- 页面配置在 Vue 文件的 `route-block` 中配置
- 应用配置在 `manifest.config.ts` 中修改

#### 问题：如何设置首页？
**解决方案：** 在目标页面的 Vue 文件中设置：
```vue
<route lang="json">
{
  "type": "home"
}
</route>
```
**注意：** 确保项目中只有一个页面设置为首页。

#### 问题：如何配置分包？
**解决方案：** 在 `vite.config.ts` 中配置：
```typescript
UniPages({
  subPackages: ['src/pages-sub'], // 分包目录
})
```

### 13.2 开发环境相关

#### 问题：首次运行小程序报错
**原因：** 缺少自动生成的配置文件。

**解决方案：**
```bash
# 先安装依赖生成配置文件
pnpm i
# 然后运行小程序
pnpm run dev:mp-weixin
```

#### 问题：Git 提交报错
**原因：** 代码不符合 ESLint 或 Commitlint 规范。

**解决方案：**
```bash
# 修复代码格式问题
pnpm run lint:fix

# 或者临时跳过检查（不推荐）
git commit --no-verify -m "commit message"
```

#### 问题：支付宝小程序运行报错
**解决方案：** 在支付宝开发者工具中勾选"本地开发跳过 ES5 转译"。

### 13.3 版本兼容性

#### 问题：Node.js 版本要求
**要求：** Node.js >= 18，推荐使用 Node.js 22+
**原因：** 低版本可能导致微信小程序编译报错。

#### 问题：pnpm 版本要求
**要求：** pnpm >= 7.30，推荐使用 pnpm 10+
**原因：** `pnpm 9 + node 18` 组合可能出现工作区配置错误。

#### 问题：uni-app 升级
**解决方案：**
```bash
# 升级 uni-app 到最新版本
npx @dcloudio/uvm@latest
```

### 13.4 环境变量使用

#### 问题：无法使用 `process.env`
**原因：** uni-app 环境限制。

**解决方案：** 使用 `import.meta.env` 替代：
```typescript
// ❌ 错误用法
const apiUrl = process.env.VITE_API_URL

// ✅ 正确用法
const apiUrl = import.meta.env.VITE_API_URL
```

---

## 规则执行

本规范通过以下工具自动执行：
- **ESLint**: 代码质量检查
- **Prettier**: 代码格式化
- **Husky**: Git hooks
- **Commitlint**: 提交信息检查
- **TypeScript**: 类型检查
- **openapi-ts-request**: 自动生成接口代码

### 常用命令
```bash
# 🔴 生成接口代码（优先执行）
pnpm run openapi-ts-request

# 代码检查
pnpm run lint

# 代码格式化
pnpm run lint:fix

# 类型检查
pnpm run type-check

# 开发环境启动
pnpm run dev:h5          # H5 开发
pnpm run dev:mp-weixin   # 微信小程序开发

# 构建命令
pnpm run build:h5        # H5 构建
pnpm run build:mp-weixin # 微信小程序构建
```

**⚠️ 重要提醒：**
- 新成员加入项目后，首先执行 `pnpm i` 生成配置文件
- 接口文档更新后，必须重新执行 `pnpm run openapi-ts-request`
- 如果遇到类型错误，优先检查是否执行了接口生成命令
- 首次运行非 H5 端前，必须先执行 `pnpm i` 生成 `src/manifest.json`

### 环境要求
- **Node.js**: >= 18，推荐 22+
- **pnpm**: >= 7.30，推荐 10+
- **微信开发者工具**: 推荐最新稳定版

### 故障排除
```bash
# 如果遇到构建问题，尝试以下步骤：
rm -rf node_modules
rm pnpm-lock.yaml
pnpm i

# 升级 uni-app 版本
npx @dcloudio/uvm@latest

# 临时跳过 Git 检查（紧急情况）
git commit --no-verify -m "commit message"
```

违反规范的代码将无法通过 CI/CD 检查，请严格遵守以上规范。