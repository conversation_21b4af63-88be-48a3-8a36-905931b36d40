/**
 * 抖音相关API接口
 * 按照Unibest规范使用封装的http请求库
 */

import type {
  DouyinContentType,
  DouyinData,
  DouyinParseParams,
} from './types/douyin'
import { http } from '@/http/http'
import { getDouyinContentType } from './types/douyin'

/**
 * 抖音去水印解析
 * @param params 解析参数 - { url: string }
 * @returns Promise<DouyinData>
 */
export async function parseDouyin(params: DouyinParseParams): Promise<DouyinData> {
  const response = await http.get<DouyinData>('/douyin/video', params)
  return response.data
}

/**
 * 获取抖音内容类型
 * @param url 抖音链接
 * @returns Promise<DouyinContentType>
 */
export async function getDouyinType(url: string): Promise<DouyinContentType> {
  const data = await parseDouyin({ url })
  return getDouyinContentType(data)
}
