/**
 * 抖音API相关类型定义
 */

// 抖音内容数据（根据真实API返回格式定义）
export interface DouyinData {
  title: string
  author: string
  like: number
  time: number
  // 视频相关字段（可选）
  video_url?: string
  // 图集相关字段（可选）
  count?: number
  images?: string[]
}

// 抖音API响应格式
export interface DouyinApiResponse {
  code: number
  message: string
  data: DouyinData
}

// 抖音解析请求参数
export interface DouyinParseParams {
  url: string
}

// 内容类型枚举
export enum DouyinContentType {
  VIDEO = 'video',
  IMAGE = 'image',
}

// 类型守卫函数
export function isDouyinVideo(data: DouyinData): data is DouyinData & { video_url: string } {
  return !!data.video_url
}

export function isDouyinImage(data: DouyinData): data is DouyinData & { images: string[], count: number } {
  return !!data.images && !!data.count
}

// 获取内容类型的工具函数
export function getDouyinContentType(data: DouyinData): DouyinContentType {
  return isDouyinVideo(data) ? DouyinContentType.VIDEO : DouyinContentType.IMAGE
}
