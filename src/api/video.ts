import { http } from '@/http/http'

/**
 * 视频解析相关API接口定义
 */

// 视频解析API端点
const API_ENDPOINT = '/app/dspjx/video'

/**
 * 视频解析响应数据类型
 */
export interface VideoParseResponse {
  code: number
  message: string
  data: {
    type: 'video' | 'img'
    title?: string
    author?: string
    url?: string
    video_url?: string
    images?: string[]
    imgs?: string[]
    cover?: string
    duration?: number
    size?: number
    originalUrl?: string
  }
}

/**
 * 解析视频或图集链接
 * @param url 内容分享链接（视频或图集）
 * @returns 解析结果
 */
export function parseContent(url: string) {
  return http.Get<VideoParseResponse>(API_ENDPOINT, {
    params: { url }
  })
}

/**
 * 判断内容类型是否为图集
 * @param data API返回的数据
 * @returns 是否为图集类型
 */
export function isImageCollection(data: VideoParseResponse['data']): boolean {
  return data && data.type === 'img'
}

/**
 * 判断内容类型是否为视频
 * @param data API返回的数据
 * @returns 是否为视频类型
 */
export function isVideo(data: VideoParseResponse['data']): boolean {
  return data && data.type === 'video'
}

/**
 * 获取图集图片数组
 * @param data API返回的数据
 * @returns 图片URL数组
 */
export function getImageUrls(data: VideoParseResponse['data']): string[] {
  if (!data) return []
  
  // 获取图片数组，支持多种字段名
  if (Array.isArray(data.images) && data.images.length > 0) {
    return data.images
  }
  
  if (Array.isArray(data.imgs) && data.imgs.length > 0) {
    return data.imgs
  }
  
  return []
}

/**
 * 获取视频URL
 * @param data API返回的数据
 * @returns 视频URL
 */
export function getVideoUrl(data: VideoParseResponse['data']): string {
  if (!data) return ''
  
  // 支持多种字段名
  return data.url || data.video_url || ''
}

/**
 * 获取内容标题
 * @param data API返回的数据
 * @returns 内容标题
 */
export function getContentTitle(data: VideoParseResponse['data']): string {
  return data?.title || '未知标题'
}

/**
 * 获取内容作者
 * @param data API返回的数据
 * @returns 内容作者
 */
export function getContentAuthor(data: VideoParseResponse['data']): string {
  return data?.author || '未知作者'
}

/**
 * 获取内容封面
 * @param data API返回的数据
 * @returns 封面URL
 */
export function getContentCover(data: VideoParseResponse['data']): string {
  return data?.cover || ''
}
