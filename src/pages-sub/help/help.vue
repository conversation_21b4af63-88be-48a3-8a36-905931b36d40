<route lang="jsonc">
{
  "style": {
    "navigationBarTitleText": "帮助与反馈",
    "navigationBarBackgroundColor": "#FFFFFF",
    "navigationBarTextStyle": "black"
  }
}
</route>

<template>
  <view class="min-h-screen bg-gray-50" :style="safeAreaStyle">
    <!-- 使用说明 -->
    <view class="p-6 mb-6 bg-white shadow-sm rounded-2xl">
      <view class="flex items-center mb-4">
        <text class="i-carbon-help text-blue-500 text-xl mr-2"></text>
        <text class="text-lg font-semibold text-gray-800">使用说明</text>
      </view>
      
      <view class="space-y-4">
        <view class="help-item">
          <text class="help-step">1</text>
          <view class="help-content">
            <text class="help-title">复制分享链接</text>
            <text class="help-desc">在抖音、快手、小红书等平台复制视频或图集的分享链接</text>
          </view>
        </view>
        
        <view class="help-item">
          <text class="help-step">2</text>
          <view class="help-content">
            <text class="help-title">粘贴到应用</text>
            <text class="help-desc">打开趣水印，将链接粘贴到输入框中</text>
          </view>
        </view>
        
        <view class="help-item">
          <text class="help-step">3</text>
          <view class="help-content">
            <text class="help-title">开始解析</text>
            <text class="help-desc">点击"开始解析"按钮，等待解析完成</text>
          </view>
        </view>
        
        <view class="help-item">
          <text class="help-step">4</text>
          <view class="help-content">
            <text class="help-title">下载保存</text>
            <text class="help-desc">解析成功后，可以下载视频或保存图片到相册</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 支持平台 -->
    <view class="p-6 mb-6 bg-white shadow-sm rounded-2xl">
      <view class="flex items-center mb-4">
        <text class="i-carbon-platforms text-green-500 text-xl mr-2"></text>
        <text class="text-lg font-semibold text-gray-800">支持平台</text>
      </view>
      
      <view class="grid grid-cols-2 gap-3">
        <view 
          v-for="platform in supportedPlatforms" 
          :key="platform.name"
          class="flex items-center p-3 bg-gray-50 rounded-lg"
        >
          <text :class="platform.icon" class="text-lg mr-2" :style="{ color: platform.color }"></text>
          <text class="text-sm text-gray-700">{{ platform.name }}</text>
        </view>
      </view>
    </view>
    
    <!-- 常见问题 -->
    <view class="p-6 mb-6 bg-white shadow-sm rounded-2xl">
      <view class="flex items-center mb-4">
        <text class="i-carbon-help-desk text-orange-500 text-xl mr-2"></text>
        <text class="text-lg font-semibold text-gray-800">常见问题</text>
      </view>
      
      <view class="space-y-4">
        <view 
          v-for="(faq, index) in faqs" 
          :key="index"
          class="faq-item"
          @click="toggleFaq(index)"
        >
          <view class="flex items-center justify-between py-3">
            <text class="text-sm font-medium text-gray-800 flex-1">{{ faq.question }}</text>
            <text 
              class="text-gray-400 transition-transform duration-200"
              :class="[
                expandedFaq === index ? 'i-carbon-chevron-up' : 'i-carbon-chevron-down'
              ]"
            ></text>
          </view>
          <view 
            v-if="expandedFaq === index"
            class="pb-3 text-sm text-gray-600 leading-relaxed"
          >
            {{ faq.answer }}
          </view>
        </view>
      </view>
    </view>
    
    <!-- 反馈联系 -->
    <view class="p-6 mb-6 bg-white shadow-sm rounded-2xl">
      <view class="flex items-center mb-4">
        <text class="i-carbon-email text-purple-500 text-xl mr-2"></text>
        <text class="text-lg font-semibold text-gray-800">反馈联系</text>
      </view>
      
      <view class="space-y-3">
        <view class="flex items-center p-3 bg-gray-50 rounded-lg">
          <text class="i-carbon-user text-gray-500 mr-3"></text>
          <view class="flex-1">
            <text class="text-sm text-gray-600">开发者</text>
            <text class="block text-sm font-medium text-gray-800">迷失</text>
          </view>
        </view>
        
        <view class="flex items-center p-3 bg-gray-50 rounded-lg">
          <text class="i-carbon-version text-gray-500 mr-3"></text>
          <view class="flex-1">
            <text class="text-sm text-gray-600">当前版本</text>
            <text class="block text-sm font-medium text-gray-800">v1.0.0</text>
          </view>
        </view>
        
        <button 
          @click="handleFeedback"
          class="w-full py-3 mt-4 text-white bg-blue-500 rounded-lg active:bg-blue-600"
        >
          <text class="i-carbon-send mr-2"></text>
          <text>意见反馈</text>
        </button>
      </view>
    </view>
    
    <!-- 免责声明 -->
    <view class="p-6 mb-20 bg-white shadow-sm rounded-2xl">
      <view class="flex items-center mb-4">
        <text class="i-carbon-warning text-red-500 text-xl mr-2"></text>
        <text class="text-lg font-semibold text-gray-800">免责声明</text>
      </view>
      
      <text class="text-sm text-gray-600 leading-relaxed">
        本应用仅供学习交流使用，请勿用于商业用途。下载的内容版权归原作者所有，请尊重原创，合理使用。使用本应用产生的任何法律责任由用户自行承担。
      </text>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { getSafeAreaStyle } from '@/utils/safeArea'

// 获取安全区域样式
const safeAreaStyle = getSafeAreaStyle()

// 展开的FAQ索引
const expandedFaq = ref<number | null>(null)

// 支持的平台
const supportedPlatforms = [
  { name: '抖音', icon: 'i-carbon-video', color: '#ff0050' },
  { name: '快手', icon: 'i-carbon-video', color: '#ff6600' },
  { name: '小红书', icon: 'i-carbon-image', color: '#ff2442' },
  { name: '微博', icon: 'i-carbon-share', color: '#e6162d' },
  { name: 'B站', icon: 'i-carbon-video', color: '#00a1d6' },
  { name: '西瓜视频', icon: 'i-carbon-video', color: '#20b954' },
]

// 常见问题
const faqs = [
  {
    question: '为什么解析失败？',
    answer: '可能是链接格式不正确、网络连接问题或平台限制。请检查链接是否完整，确保网络连接正常，然后重试。'
  },
  {
    question: '支持哪些视频格式？',
    answer: '支持大部分主流视频格式，包括MP4、AVI、MOV等。具体支持格式取决于原平台提供的视频格式。'
  },
  {
    question: '下载的视频保存在哪里？',
    answer: '视频会保存到设备的相册中。在相册的"视频"或"下载"文件夹中可以找到下载的内容。'
  },
  {
    question: '为什么有些视频无法下载？',
    answer: '部分视频可能受到版权保护或平台限制，无法下载。建议尊重原创，仅下载允许分享的内容。'
  },
  {
    question: '应用安全吗？',
    answer: '应用不会收集用户隐私信息，所有解析过程在本地进行。但请注意网络安全，避免在不安全的网络环境下使用。'
  }
]

// 切换FAQ展开状态
const toggleFaq = (index: number) => {
  expandedFaq.value = expandedFaq.value === index ? null : index
}

// 处理反馈
const handleFeedback = () => {
  // #ifdef MP-WEIXIN
  uni.showModal({
    title: '意见反馈',
    content: '感谢您的反馈！您可以通过微信群或其他方式联系我们。',
    showCancel: false,
    confirmText: '知道了'
  })
  // #endif
  
  // #ifndef MP-WEIXIN
  uni.showToast({
    title: '感谢您的反馈！',
    icon: 'success'
  })
  // #endif
}
</script>

<style scoped>
.space-y-4 > view:not(:first-child) {
  margin-top: 1rem;
}

.space-y-3 > view:not(:first-child) {
  margin-top: 0.75rem;
}

.help-item {
  display: flex;
  align-items: flex-start;
}

.help-step {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  background: linear-gradient(135deg, #3B82F6, #1D4ED8);
  color: white;
  border-radius: 50%;
  font-size: 12px;
  font-weight: bold;
  margin-right: 12px;
  margin-top: 2px;
  flex-shrink: 0;
}

.help-content {
  flex: 1;
}

.help-title {
  display: block;
  font-weight: 600;
  color: #374151;
  margin-bottom: 4px;
}

.help-desc {
  display: block;
  font-size: 14px;
  color: #6B7280;
  line-height: 1.5;
}

.faq-item {
  border-bottom: 1px solid #F3F4F6;
}

.faq-item:last-child {
  border-bottom: none;
}

.grid {
  display: grid;
}

.grid-cols-2 {
  grid-template-columns: repeat(2, 1fr);
}

.gap-3 {
  gap: 0.75rem;
}
</style>
