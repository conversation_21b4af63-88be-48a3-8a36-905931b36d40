<route lang="jsonc">
{
  "style": {
    "navigationBarTitleText": "下载记录",
    "navigationBarBackgroundColor": "#FFFFFF",
    "navigationBarTextStyle": "black",
    "enablePullDownRefresh": true
  }
}
</route>

<template>
  <view class="min-h-screen bg-gray-50" :style="safeAreaStyle">
    <!-- 筛选栏 -->
    <scroll-view scroll-x class="pb-2 mb-4 whitespace-nowrap" show-scrollbar="false">
      <view class="inline-block mr-3" @click="setFilter('all')">
        <view 
          class="px-4 py-2 text-sm rounded-full"
          :class="currentFilter === 'all' ? 'text-white bg-blue-500' : 'text-gray-600 bg-white'"
        >
          全部
        </view>
      </view>
      <view class="inline-block mr-3" @click="setFilter('video')">
        <view 
          class="px-4 py-2 text-sm rounded-full"
          :class="currentFilter === 'video' ? 'text-white bg-blue-500' : 'text-gray-600 bg-white'"
        >
          视频
        </view>
      </view>
      <view class="inline-block mr-3" @click="setFilter('img')">
        <view 
          class="px-4 py-2 text-sm rounded-full"
          :class="currentFilter === 'img' ? 'text-white bg-blue-500' : 'text-gray-600 bg-white'"
        >
          图集
        </view>
      </view>
    </scroll-view>
    
    <!-- 空状态 -->
    <view v-if="filteredHistory.length === 0" class="p-6 text-center bg-white shadow-sm rounded-2xl">
      <text class="text-gray-400 block mb-2">暂无下载记录</text>
      <text class="text-sm text-gray-300">解析成功的内容会显示在这里</text>
    </view>
    
    <!-- 历史记录列表 -->
    <view v-else>
      <!-- 今天 -->
      <view v-if="groupedHistory.today.length > 0" class="date-divider">
        <text class="text-xs font-medium text-gray-500">今天</text>
      </view>
      
      <view 
        v-for="(item, index) in groupedHistory.today" 
        :key="`today-${index}`" 
        class="history-item"
        @click="navigateToContent(item)"
      >
        <view class="relative flex-shrink-0 w-20 h-20 mr-3 overflow-hidden bg-gray-100 rounded-lg">
          <image
            v-if="item.cover"
            :src="item.cover"
            class="object-cover w-full h-full"
            mode="aspectFill"
          ></image>
          <view class="absolute p-1 text-xs text-white bg-black rounded top-1 right-1 bg-opacity-60">
            <text class="text-xs" :class="item.type === 'video' ? 'i-carbon-play' : 'i-carbon-image'"></text>
            <text v-if="item.type === 'img'"> ({{ item.imageCount || 0 }})</text>
            <text v-else> 视频</text>
          </view>
        </view>
        <view class="flex-1 min-w-0">
          <text class="mb-1 text-sm font-medium line-clamp-1">{{ formatTitle(item) }}</text>
          <text class="mb-2 text-xs text-gray-500">{{ item.time }}</text>
          <view class="flex items-center justify-between">
            <text class="text-xs text-gray-400">{{ formatSize(item.size) }}</text>
            <button 
              @click.stop="deleteItem(item.id)"
              class="p-1 text-gray-400 hover:text-red-500"
            >
              <text class="i-carbon-trash-can text-sm"></text>
            </button>
          </view>
        </view>
      </view>
      
      <!-- 昨天 -->
      <view v-if="groupedHistory.yesterday.length > 0" class="date-divider">
        <text class="text-xs font-medium text-gray-500">昨天</text>
      </view>
      
      <view 
        v-for="(item, index) in groupedHistory.yesterday" 
        :key="`yesterday-${index}`" 
        class="history-item"
        @click="navigateToContent(item)"
      >
        <view class="relative flex-shrink-0 w-20 h-20 mr-3 overflow-hidden bg-gray-100 rounded-lg">
          <image
            v-if="item.cover"
            :src="item.cover"
            class="object-cover w-full h-full"
            mode="aspectFill"
          ></image>
          <view class="absolute p-1 text-xs text-white bg-black rounded top-1 right-1 bg-opacity-60">
            <text class="text-xs" :class="item.type === 'video' ? 'i-carbon-play' : 'i-carbon-image'"></text>
            <text v-if="item.type === 'img'"> ({{ item.imageCount || 0 }})</text>
            <text v-else> 视频</text>
          </view>
        </view>
        <view class="flex-1 min-w-0">
          <text class="mb-1 text-sm font-medium line-clamp-1">{{ formatTitle(item) }}</text>
          <text class="mb-2 text-xs text-gray-500">{{ item.time }}</text>
          <view class="flex items-center justify-between">
            <text class="text-xs text-gray-400">{{ formatSize(item.size) }}</text>
            <button 
              @click.stop="deleteItem(item.id)"
              class="p-1 text-gray-400 hover:text-red-500"
            >
              <text class="i-carbon-trash-can text-sm"></text>
            </button>
          </view>
        </view>
      </view>
      
      <!-- 更早 -->
      <view v-if="groupedHistory.earlier.length > 0" class="date-divider">
        <text class="text-xs font-medium text-gray-500">更早</text>
      </view>
      
      <view 
        v-for="(item, index) in groupedHistory.earlier" 
        :key="`earlier-${index}`" 
        class="history-item"
        @click="navigateToContent(item)"
      >
        <view class="relative flex-shrink-0 w-20 h-20 mr-3 overflow-hidden bg-gray-100 rounded-lg">
          <image
            v-if="item.cover"
            :src="item.cover"
            class="object-cover w-full h-full"
            mode="aspectFill"
          ></image>
          <view class="absolute p-1 text-xs text-white bg-black rounded top-1 right-1 bg-opacity-60">
            <text class="text-xs" :class="item.type === 'video' ? 'i-carbon-play' : 'i-carbon-image'"></text>
            <text v-if="item.type === 'img'"> ({{ item.imageCount || 0 }})</text>
            <text v-else> 视频</text>
          </view>
        </view>
        <view class="flex-1 min-w-0">
          <text class="mb-1 text-sm font-medium line-clamp-1">{{ formatTitle(item) }}</text>
          <text class="mb-2 text-xs text-gray-500">{{ item.time }}</text>
          <view class="flex items-center justify-between">
            <text class="text-xs text-gray-400">{{ formatSize(item.size) }}</text>
            <button 
              @click.stop="deleteItem(item.id)"
              class="p-1 text-gray-400 hover:text-red-500"
            >
              <text class="i-carbon-trash-can text-sm"></text>
            </button>
          </view>
        </view>
      </view>
      
      <!-- 清空按钮 -->
      <view v-if="filteredHistory.length > 0" class="p-6 mt-6 text-center">
        <button 
          @click="clearAllHistory"
          class="px-6 py-2 text-sm text-red-500 border border-red-200 rounded-lg active:bg-red-50"
        >
          清空所有记录
        </button>
      </view>
    </view>
    
    <!-- 底部留白 -->
    <view class="h-20"></view>
  </view>
</template>

<script setup lang="ts">
import { computed, onMounted } from 'vue'
import { getSafeAreaStyle } from '@/utils/safeArea'
import { useHistoryStore } from '@/store/history'
import type { HistoryItem } from '@/types/history'

// 获取安全区域样式
const safeAreaStyle = getSafeAreaStyle()

// 使用历史记录状态管理
const historyStore = useHistoryStore()

// 计算属性
const currentFilter = computed(() => historyStore.currentFilter)
const filteredHistory = computed(() => historyStore.filteredHistory)
const groupedHistory = computed(() => historyStore.groupedHistory)

// 设置筛选条件
const setFilter = (filter: 'all' | 'video' | 'img') => {
  historyStore.setFilter(filter)
}

// 格式化标题
const formatTitle = (item: HistoryItem): string => {
  return item.title || item.desc || '未知标题'
}

// 格式化文件大小
const formatSize = (size: number | string | undefined): string => {
  if (!size) return '未知大小'
  
  let sizeNum = 0
  if (typeof size === 'string') {
    if (size.includes('KB') || size.includes('MB') || size.includes('GB')) {
      return size
    }
    sizeNum = parseInt(size)
  } else {
    sizeNum = size
  }
  
  if (!sizeNum || isNaN(sizeNum)) return '未知大小'
  
  if (sizeNum < 1024) {
    return `${sizeNum}B`
  } else if (sizeNum < 1024 * 1024) {
    return `${(sizeNum / 1024).toFixed(1)}KB`
  } else if (sizeNum < 1024 * 1024 * 1024) {
    return `${(sizeNum / (1024 * 1024)).toFixed(1)}MB`
  } else {
    return `${(sizeNum / (1024 * 1024 * 1024)).toFixed(1)}GB`
  }
}

// 导航到内容详情
const navigateToContent = (item: HistoryItem) => {
  // 根据内容类型导航到不同页面
  if (item.type === 'video') {
    uni.navigateTo({
      url: `/pages/result/result?id=${item.id}`
    })
  } else {
    uni.navigateTo({
      url: `/pages/result-album/result-album?id=${item.id}`
    })
  }
}

// 删除单条记录
const deleteItem = (id: string) => {
  uni.showModal({
    title: '确认删除',
    content: '确定要删除这条记录吗？',
    success: (res) => {
      if (res.confirm) {
        historyStore.removeHistory(id)
        uni.showToast({
          title: '删除成功',
          icon: 'success'
        })
      }
    }
  })
}

// 清空所有记录
const clearAllHistory = () => {
  uni.showModal({
    title: '确认清空',
    content: '确定要清空所有下载记录吗？此操作不可恢复。',
    success: (res) => {
      if (res.confirm) {
        historyStore.clearHistory()
        uni.showToast({
          title: '清空成功',
          icon: 'success'
        })
      }
    }
  })
}

// 页面加载时获取历史记录
onMounted(() => {
  historyStore.loadHistory()
})

// 下拉刷新
defineExpose({
  onPullDownRefresh() {
    historyStore.loadHistory()
    setTimeout(() => {
      uni.stopPullDownRefresh()
    }, 500)
  }
})
</script>

<style scoped>
.whitespace-nowrap {
  white-space: nowrap;
}

.inline-block {
  display: inline-block;
}

.date-divider {
  position: relative;
  text-align: center;
  margin: 16px 0;
}

.date-divider::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1px;
  background-color: #e5e7eb;
  z-index: 0;
}

.date-divider text {
  position: relative;
  background-color: #f7f8fa;
  padding: 0 10px;
  z-index: 1;
}

.history-item {
  display: flex;
  padding: 12px;
  margin-bottom: 12px;
  background-color: white;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border-radius: 16px;
}

.line-clamp-1 {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.min-w-0 {
  min-width: 0;
}
</style>
