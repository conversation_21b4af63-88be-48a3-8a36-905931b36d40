<route lang="jsonc">
{
  "style": {
    "navigationBarTitleText": "图集详情",
    "navigationBarBackgroundColor": "#FFFFFF",
    "navigationBarTextStyle": "black"
  }
}
</route>

<script setup lang="ts">
import { computed, onMounted, ref } from 'vue'
import { addDownloadRecord } from '@/utils/historyManager'
import { getSafeAreaStyle } from '@/utils/safeArea'

// 获取安全区域样式
const safeAreaStyle = getSafeAreaStyle()

// 响应式数据
const albumData = ref<any>(null)
const isDownloading = ref(false)
const isSaving = ref(false)
const downloadProgress = ref(0)
const currentImageIndex = ref(0)
const isSelectMode = ref(false)
const selectedImages = ref<string[]>([])

// 计算属性
const totalImages = computed(() => albumData.value?.images?.length || 0)



// 格式化总大小
function formatTotalSize(): string {
  const imageCount = albumData.value?.images?.length || 0
  const estimatedSize = imageCount * 500 * 1024 // 假设每张图片500KB

  if (estimatedSize < 1024 * 1024) {
    return `约 ${(estimatedSize / 1024).toFixed(0)}KB`
  }
  else {
    return `约 ${(estimatedSize / (1024 * 1024)).toFixed(1)}MB`
  }
}

// 处理图片点击
function handleImageClick(image: string, index: number) {
  if (isSelectMode.value) {
    // 选择模式：切换选中状态
    const selectedIndex = selectedImages.value.indexOf(image)
    if (selectedIndex > -1) {
      selectedImages.value.splice(selectedIndex, 1)
    }
    else {
      selectedImages.value.push(image)
    }
  }
  else {
    // 预览模式：打开图片预览
    uni.previewImage({
      urls: albumData.value?.images || [],
      current: index,
    })
  }
}

// 切换选择模式
function toggleSelectMode() {
  isSelectMode.value = !isSelectMode.value
  if (!isSelectMode.value) {
    selectedImages.value = []
  }
}

// 全选
function selectAll() {
  selectedImages.value = [...(albumData.value?.images || [])]
}

// 清空选择
function clearSelection() {
  selectedImages.value = []
}

// 下载所有图片
async function downloadAllImages() {
  if (!albumData.value?.images?.length)
    return

  isDownloading.value = true
  downloadProgress.value = 0
  currentImageIndex.value = 0

  try {
    const images = albumData.value.images
    for (let i = 0; i < images.length; i++) {
      currentImageIndex.value = i + 1
      downloadProgress.value = Math.round(((i + 1) / images.length) * 100)

      // 模拟下载延迟
      await new Promise(resolve => setTimeout(resolve, 500))
    }

    // 下载完成后添加到历史记录
    if (albumData.value) {
      await addDownloadRecord({
        type: 'img',
        title: albumData.value.title,
        author: albumData.value.author,
        cover: albumData.value.images?.[0], // 使用第一张图片作为封面
        images: albumData.value.images,
        imageCount: albumData.value.images?.length,
        originalUrl: albumData.value.originalUrl,
      })
    }

    uni.showToast({
      title: '下载完成',
      icon: 'success',
    })
  }
  catch (error) {
    console.error('下载失败:', error)
    uni.showToast({
      title: '下载失败',
      icon: 'none',
    })
  }
  finally {
    isDownloading.value = false
    downloadProgress.value = 0
  }
}

// 保存所有图片到相册
async function saveAllToAlbum() {
  if (!albumData.value?.images?.length)
    return

  isSaving.value = true

  try {
    // 这里应该调用实际的保存到相册功能
    setTimeout(async () => {
      isSaving.value = false

      // 保存成功后添加到历史记录
      if (albumData.value) {
        await addDownloadRecord({
          type: 'img',
          title: albumData.value.title,
          author: albumData.value.author,
          cover: albumData.value.images?.[0], // 使用第一张图片作为封面
          images: albumData.value.images,
          imageCount: albumData.value.images?.length,
          originalUrl: albumData.value.originalUrl,
        })
      }

      uni.showToast({
        title: '保存成功',
        icon: 'success',
      })
    }, 2000)
  }
  catch (error) {
    console.error('保存失败:', error)
    uni.showToast({
      title: '保存失败',
      icon: 'none',
    })
    isSaving.value = false
  }
}

// 下载选中的图片
function downloadSelectedImages() {
  if (selectedImages.value.length === 0)
    return

  uni.showToast({
    title: `开始下载 ${selectedImages.value.length} 张图片`,
    icon: 'success',
  })

  // 这里应该实现选中图片的下载逻辑
}

// 保存选中的图片到相册
function saveSelectedToAlbum() {
  if (selectedImages.value.length === 0)
    return

  uni.showToast({
    title: `开始保存 ${selectedImages.value.length} 张图片`,
    icon: 'success',
  })

  // 这里应该实现选中图片的保存逻辑
}

// 分享图集
function shareAlbum() {
  // #ifdef MP-WEIXIN
  uni.showShareMenu({
    withShareTicket: true,
  })
  // #endif

  // #ifndef MP-WEIXIN
  uni.showToast({
    title: '分享功能开发中',
    icon: 'none',
  })
  // #endif
}

// 重新解析内容
function reParseContent() {
  uni.showModal({
    title: '重新解析',
    content: '确定要重新解析这个内容吗？',
    success: (res) => {
      if (res.confirm) {
        uni.showToast({
          title: '功能开发中',
          icon: 'none',
        })
      }
    },
  })
}

// 预览图片
function previewImages() {
  if (!albumData.value?.images?.length)
    return

  uni.previewImage({
    urls: albumData.value.images,
    current: 0,
  })
}

// 页面加载时获取图集数据
onMounted(() => {
  // 获取页面参数
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1]
  const options = currentPage.options || {}

  if (options.data) {
    try {
      // 从首页传递过来的真实数据
      const decodedData = decodeURIComponent(options.data)
      const parsedData = JSON.parse(decodedData)
      albumData.value = parsedData
      console.log('图集数据:', parsedData)
    }
    catch (error) {
      console.error('解析图集数据失败:', error)
      uni.showToast({
        title: '数据解析失败',
        icon: 'none',
      })
      // 返回上一页
      setTimeout(() => {
        uni.navigateBack()
      }, 1500)
    }
  }
  else if (options.id) {
    // 根据ID获取数据（暂时不实现）
    uni.showToast({
      title: '功能开发中',
      icon: 'none',
    })
    setTimeout(() => {
      uni.navigateBack()
    }, 1500)
  }
  else {
    // 没有数据，返回上一页
    uni.showToast({
      title: '缺少必要参数',
      icon: 'none',
    })
    setTimeout(() => {
      uni.navigateBack()
    }, 1500)
  }
})
</script>

<template>
  <view class="min-h-screen bg-gray-50" :style="safeAreaStyle">
    <!-- 图集信息 -->
    <view class="mb-6 rounded-2xl bg-white p-6 shadow-sm">
      <text class="line-clamp-2 mb-2 text-lg text-gray-800 font-semibold">
        {{ albumData?.title || '图集标题' }}
      </text>
      <text class="mb-4 text-sm text-gray-500">
        {{ albumData?.author || '未知作者' }}
      </text>

      <view class="flex items-center text-xs text-gray-400 space-x-4">
        <view class="flex items-center">
          <text class="i-carbon-image mr-1" />
          <text>{{ albumData?.count || albumData?.images?.length || 0 }} 张图片</text>
        </view>
        <view class="flex items-center">
          <text class="i-carbon-data-1 mr-1" />
          <text>{{ formatTotalSize() }}</text>
        </view>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="mb-6 rounded-2xl bg-white p-6 shadow-sm">
      <view class="grid grid-cols-2 gap-3">
        <button
          :disabled="isDownloading"
          class="flex items-center justify-center rounded-xl bg-blue-500 py-4 text-white active:bg-blue-600 disabled:bg-gray-300"
          @click="downloadAllImages"
        >
          <text class="i-carbon-download mr-2" />
          <text v-if="isDownloading">
            下载中...
          </text>
          <text v-else>
            全部下载
          </text>
        </button>

        <button
          :disabled="isSaving"
          class="flex items-center justify-center rounded-xl bg-green-500 py-4 text-white active:bg-green-600 disabled:bg-gray-300"
          @click="saveAllToAlbum"
        >
          <text class="i-carbon-save mr-2" />
          <text v-if="isSaving">
            保存中...
          </text>
          <text v-else>
            保存相册
          </text>
        </button>
      </view>

      <button
        class="mt-3 w-full flex items-center justify-center rounded-xl bg-gray-100 py-4 text-gray-700 active:bg-gray-200"
        @click="shareAlbum"
      >
        <text class="i-carbon-share mr-2" />
        <text>分享图集</text>
      </button>
    </view>

    <!-- 下载进度 -->
    <view v-if="downloadProgress > 0" class="mb-6 rounded-2xl bg-white p-6 shadow-sm">
      <view class="mb-2 flex justify-between text-sm text-gray-600">
        <text>下载进度</text>
        <text>{{ currentImageIndex }}/{{ totalImages }} ({{ downloadProgress }}%)</text>
      </view>
      <view class="h-2 w-full rounded-full bg-gray-200">
        <view
          class="h-2 rounded-full bg-blue-500 transition-all duration-300"
          :style="{ width: `${downloadProgress}%` }"
        />
      </view>
    </view>

    <!-- 图片网格 -->
    <view class="mb-6 rounded-2xl bg-white p-6 shadow-sm">
      <view class="mb-4 flex items-center justify-between">
        <text class="text-lg text-gray-800 font-semibold">
          图片预览
        </text>
        <view class="flex items-center">
          <button
            class="rounded-lg bg-blue-50 px-3 py-1 text-sm text-blue-500 active:bg-blue-100"
            @click="toggleSelectMode"
          >
            {{ isSelectMode ? '取消选择' : '批量选择' }}
          </button>
        </view>
      </view>

      <!-- 全选/取消全选 -->
      <view v-if="isSelectMode" class="mb-4 flex items-center justify-between rounded-lg bg-blue-50 p-3">
        <text class="text-sm text-gray-700">
          已选择 {{ selectedImages.length }} 张图片
        </text>
        <view class="flex space-x-2">
          <button
            class="rounded bg-white px-3 py-1 text-xs text-blue-500 active:bg-gray-50"
            @click="selectAll"
          >
            全选
          </button>
          <button
            class="rounded bg-white px-3 py-1 text-xs text-gray-500 active:bg-gray-50"
            @click="clearSelection"
          >
            清空
          </button>
        </view>
      </view>

      <!-- 图片网格 -->
      <view class="grid grid-cols-3 gap-2">
        <view
          v-for="(image, index) in albumData?.images || []"
          :key="index"
          class="relative aspect-square overflow-hidden rounded-lg bg-gray-100"
          @click="handleImageClick(image, index)"
        >
          <image
            :src="image"
            class="h-full w-full object-cover"
            mode="aspectFill"
            :lazy-load="true"
          />

          <!-- 选择模式的复选框 -->
          <view
            v-if="isSelectMode"
            class="absolute right-2 top-2 h-6 w-6 flex items-center justify-center border-2 border-white rounded-full bg-black bg-opacity-30"
            :class="selectedImages.includes(image) ? 'bg-blue-500' : ''"
          >
            <text
              v-if="selectedImages.includes(image)"
              class="i-carbon-checkmark text-sm text-white"
            />
          </view>

          <!-- 图片序号 -->
          <view class="absolute bottom-1 left-1 rounded bg-black bg-opacity-60 px-2 py-1 text-xs text-white">
            {{ index + 1 }}
          </view>
        </view>
      </view>

      <!-- 批量操作按钮 -->
      <view v-if="isSelectMode && selectedImages.length > 0" class="grid grid-cols-2 mt-4 gap-3">
        <button
          class="rounded-lg bg-blue-500 py-3 text-white active:bg-blue-600"
          @click="downloadSelectedImages"
        >
          下载选中 ({{ selectedImages.length }})
        </button>
        <button
          class="rounded-lg bg-green-500 py-3 text-white active:bg-green-600"
          @click="saveSelectedToAlbum"
        >
          保存选中 ({{ selectedImages.length }})
        </button>
      </view>
    </view>

    <!-- 相关操作 -->
    <view class="mb-6 rounded-2xl bg-white p-6 shadow-sm">
      <text class="mb-4 text-lg text-gray-800 font-semibold">
        相关操作
      </text>

      <view class="space-y-3">
        <view
          class="flex items-center rounded-lg bg-gray-50 p-3 active:bg-gray-100"
          @click="reParseContent"
        >
          <text class="i-carbon-renew mr-3 text-blue-500" />
          <text class="text-sm text-gray-700">
            重新解析
          </text>
        </view>

        <view
          class="flex items-center rounded-lg bg-gray-50 p-3 active:bg-gray-100"
          @click="previewImages"
        >
          <text class="i-carbon-view mr-3 text-green-500" />
          <text class="text-sm text-gray-700">
            图片预览
          </text>
        </view>
      </view>
    </view>
  </view>
</template>

<style scoped>
.line-clamp-2 {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.space-x-4 > view:not(:first-child) {
  margin-left: 1rem;
}

.space-x-2 > button:not(:first-child) {
  margin-left: 0.5rem;
}

.space-y-3 > view:not(:first-child) {
  margin-top: 0.75rem;
}

.grid {
  display: grid;
}

.grid-cols-2 {
  grid-template-columns: repeat(2, 1fr);
}

.grid-cols-3 {
  grid-template-columns: repeat(3, 1fr);
}

.gap-2 {
  gap: 0.5rem;
}

.gap-3 {
  gap: 0.75rem;
}

.aspect-square {
  aspect-ratio: 1 / 1;
}
</style>
