<route lang="jsonc">
{
  "style": {
    "navigationBarTitleText": "视频详情",
    "navigationBarBackgroundColor": "#FFFFFF",
    "navigationBarTextStyle": "black"
  }
}
</route>

<script setup lang="ts">
import { onMounted, ref } from 'vue'
import dayjs from 'dayjs'
import relativeTime from 'dayjs/plugin/relativeTime'
import 'dayjs/locale/zh-cn'
import { addDownloadRecord } from '@/utils/historyManager'
import { getSafeAreaStyle } from '@/utils/safeArea'

// 配置 dayjs
dayjs.extend(relativeTime)
dayjs.locale('zh-cn')

// 获取安全区域样式
const safeAreaStyle = getSafeAreaStyle()

// 响应式数据
const videoData = ref<any>(null)

// 下载视频
async function downloadVideo() {
  if (!videoData.value?.video_url)
    return

  try {
    // 下载成功后添加到历史记录
    if (videoData.value) {
      await addDownloadRecord({
        type: 'video',
        title: videoData.value.title,
        author: videoData.value.author,
        cover: videoData.value.cover,
        url: videoData.value.video_url,
        size: videoData.value.size,
        duration: videoData.value.duration,
        originalUrl: videoData.value.originalUrl,
      })
    }

    uni.showToast({
      title: '下载完成',
      icon: 'success',
    })
  }
  catch (error) {
    console.error('下载失败:', error)
    uni.showToast({
      title: '下载失败',
      icon: 'none',
    })
  }
}

// 保存到相册
async function saveToAlbum() {
  if (!videoData.value?.video_url)
    return

  try {
    // 保存成功后添加到历史记录
    if (videoData.value) {
      await addDownloadRecord({
        type: 'video',
        title: videoData.value.title,
        author: videoData.value.author,
        cover: videoData.value.cover,
        url: videoData.value.video_url,
        size: videoData.value.size,
        duration: videoData.value.duration,
        originalUrl: videoData.value.originalUrl,
      })
    }

    uni.showToast({
      title: '保存成功',
      icon: 'success',
    })
  }
  catch (error) {
    console.error('保存失败:', error)
    uni.showToast({
      title: '保存失败',
      icon: 'none',
    })
  }
}





// 复制视频链接
function copyVideoUrl() {
  if (!videoData.value?.video_url)
    return

  uni.setClipboardData({
    data: videoData.value.video_url,
    success: () => {
      uni.showToast({
        title: '链接已复制',
        icon: 'success',
      })
    },
  })
}

// 页面加载时获取视频数据
onMounted(() => {
  // 获取页面参数
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1]
  const options = currentPage.options || {}

  if (options.data) {
    // 从URL参数中获取解析结果数据
    try {
      const parsedData = JSON.parse(decodeURIComponent(options.data))
      videoData.value = parsedData
    }
    catch (error) {
      console.error('解析数据失败:', error)
      uni.showToast({
        title: '数据解析失败',
        icon: 'none',
      })
      // 返回上一页
      setTimeout(() => {
        uni.navigateBack()
      }, 1500)
    }
  }
  else if (options.id) {
    // 这里应该根据ID从历史记录中获取数据
    uni.showToast({
      title: '功能开发中',
      icon: 'none',
    })
    setTimeout(() => {
      uni.navigateBack()
    }, 1500)
  }
  else {
    // 没有数据，返回上一页
    uni.showToast({
      title: '缺少数据',
      icon: 'none',
    })
    setTimeout(() => {
      uni.navigateBack()
    }, 1500)
  }
})
</script>

<template>
  <view class="min-h-screen bg-slate-100" :style="safeAreaStyle">
    <!-- 视频播放区域 -->
    <view class="relative mb-4 overflow-hidden bg-black shadow-2xl">
      <video
        v-if="videoData?.video_url"
        :src="videoData.video_url"
        :poster="videoData.cover"
        controls
        class="aspect-video w-full"
        object-fit="contain"
        show-center-play-btn
        show-play-btn
        enable-play-gesture
      />

      <!-- 加载状态 -->
      <view v-else class="aspect-video w-full flex items-center justify-center bg-gray-200">
        <view class="flex flex-col items-center">
          <text class="i-carbon-video text-6xl text-gray-400 mb-2" />
          <text class="text-gray-500 text-sm">视频加载中...</text>
        </view>
      </view>

      <!-- 视频覆盖层信息 -->
      <view v-if="videoData?.video_url" class="absolute bottom-0 left-0 right-0 video-overlay p-4">
        <view class="flex items-center justify-between">
          <view class="flex items-center">
            <view v-if="videoData?.like" class="flex items-center overlay-badge mr-3">
              <text class="i-carbon-favorite text-red-400 text-xs mr-1" />
              <text class="text-white text-xs font-medium">{{ videoData.like }}</text>
            </view>
            <view v-if="videoData?.time" class="flex items-center overlay-badge">
              <text class="i-carbon-time text-blue-400 text-xs mr-1" />
              <text class="text-white text-xs">{{ dayjs.unix(videoData.time).fromNow() }}</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 视频信息卡片 -->
    <view class="mx-4 mb-4 rounded-3xl bg-white p-6 shadow-lg border border-gray-100">
      <!-- 标题区域 -->
      <view class="mb-6">
        <text class="line-clamp-3 text-xl text-gray-900 font-bold leading-relaxed">
          {{ videoData?.title }}
        </text>
      </view>

      <!-- 作者信息 -->
      <view class="mb-6 flex items-center">
        <view class="mr-4 h-12 w-12 rounded-2xl author-avatar flex items-center justify-center shadow-lg">
          <text class="i-carbon-user text-white text-xl" />
        </view>
        <view class="flex-1">
          <text class="text-lg text-gray-800 font-semibold">
            {{ videoData?.author }}
          </text>
          <text class="text-sm text-gray-500 mt-1">创作者</text>
        </view>
      </view>
    </view>

    <!-- 操作按钮区域 -->
    <view class="mx-4 mb-4">
      <view class="grid grid-cols-3 gap-3">
        <!-- 下载按钮 -->
        <button
          :disabled="!videoData?.video_url"
          class="flex flex-col items-center justify-center rounded-2xl btn-download py-6 text-white shadow-lg disabled:bg-gray-300"
          @click="downloadVideo"
        >
          <text class="i-carbon-download text-2xl mb-2" />
          <text class="text-sm font-medium">下载</text>
        </button>

        <!-- 保存按钮 -->
        <button
          :disabled="!videoData?.video_url"
          class="flex flex-col items-center justify-center rounded-2xl btn-save py-6 text-white shadow-lg disabled:bg-gray-300"
          @click="saveToAlbum"
        >
          <text class="i-carbon-save text-2xl mb-2" />
          <text class="text-sm font-medium">保存</text>
        </button>

        <!-- 复制链接按钮 -->
        <button
          :disabled="!videoData?.video_url"
          class="flex flex-col items-center justify-center rounded-2xl btn-copy py-6 text-white shadow-lg disabled:bg-gray-300"
          @click="copyVideoUrl"
        >
          <text class="i-carbon-copy text-2xl mb-2" />
          <text class="text-sm font-medium">复制</text>
        </button>
      </view>
    </view>




  </view>
</template>

<style scoped>
.line-clamp-2 {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.line-clamp-3 {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}

.space-x-4 > view:not(:first-child) {
  margin-left: 1rem;
}

.space-y-3 > view:not(:first-child) {
  margin-top: 0.75rem;
}

.grid {
  display: grid;
}

.grid-cols-2 {
  grid-template-columns: repeat(2, 1fr);
}

.grid-cols-3 {
  grid-template-columns: repeat(3, 1fr);
}

.gap-3 {
  gap: 0.75rem;
}

.aspect-video {
  aspect-ratio: 16 / 9;
}

/* 自定义渐变和效果 */
.video-overlay {
  background: linear-gradient(to top, rgba(0, 0, 0, 0.6), transparent);
}

.overlay-badge {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  padding: 4px 8px;
  backdrop-filter: blur(4px);
}

.author-avatar {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.btn-download {
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
}

.btn-download:active {
  background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
}

.btn-save {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.btn-save:active {
  background: linear-gradient(135deg, #059669 0%, #047857 100%);
}

.btn-copy {
  background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
}

.btn-copy:active {
  background: linear-gradient(135deg, #7c3aed 0%, #6d28d9 100%);
}
</style>
