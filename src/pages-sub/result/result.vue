<route lang="jsonc">
{
  "style": {
    "navigationBarTitleText": "视频详情",
    "navigationBarBackgroundColor": "#FFFFFF",
    "navigationBarTextStyle": "black"
  }
}
</route>

<script setup lang="ts">
import { onMounted, ref } from 'vue'
import { addDownloadRecord } from '@/utils/historyManager'
import { getSafeAreaStyle } from '@/utils/safeArea'

// 获取安全区域样式
const safeAreaStyle = getSafeAreaStyle()

// 响应式数据
const videoData = ref<any>(null)

// 格式化点赞数
function formatLikeCount(count: number): string {
  if (!count) return '0'

  if (count < 1000) {
    return count.toString()
  } else if (count < 10000) {
    return `${(count / 1000).toFixed(1)}k`
  } else if (count < 100000) {
    return `${(count / 10000).toFixed(1)}w`
  } else {
    return `${Math.floor(count / 10000)}w`
  }
}

// 格式化发布时间
function formatPublishTime(timestamp: number): string {
  if (!timestamp) return ''

  const date = new Date(timestamp * 1000) // 转换为毫秒
  const now = new Date()
  const diff = now.getTime() - date.getTime()

  const minutes = Math.floor(diff / (1000 * 60))
  const hours = Math.floor(diff / (1000 * 60 * 60))
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))

  if (minutes < 60) {
    return `${minutes}分钟前`
  } else if (hours < 24) {
    return `${hours}小时前`
  } else if (days < 30) {
    return `${days}天前`
  } else {
    // 超过30天显示具体日期
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }
}



// 下载视频
async function downloadVideo() {
  if (!videoData.value?.video_url)
    return

  try {
    // 下载成功后添加到历史记录
    if (videoData.value) {
      await addDownloadRecord({
        type: 'video',
        title: videoData.value.title,
        author: videoData.value.author,
        cover: videoData.value.cover,
        url: videoData.value.video_url,
        size: videoData.value.size,
        duration: videoData.value.duration,
        originalUrl: videoData.value.originalUrl,
      })
    }

    uni.showToast({
      title: '下载完成',
      icon: 'success',
    })
  }
  catch (error) {
    console.error('下载失败:', error)
    uni.showToast({
      title: '下载失败',
      icon: 'none',
    })
  }
}

// 保存到相册
async function saveToAlbum() {
  if (!videoData.value?.video_url)
    return

  try {
    // 保存成功后添加到历史记录
    if (videoData.value) {
      await addDownloadRecord({
        type: 'video',
        title: videoData.value.title,
        author: videoData.value.author,
        cover: videoData.value.cover,
        url: videoData.value.video_url,
        size: videoData.value.size,
        duration: videoData.value.duration,
        originalUrl: videoData.value.originalUrl,
      })
    }

    uni.showToast({
      title: '保存成功',
      icon: 'success',
    })
  }
  catch (error) {
    console.error('保存失败:', error)
    uni.showToast({
      title: '保存失败',
      icon: 'none',
    })
  }
}





// 复制视频链接
function copyVideoUrl() {
  if (!videoData.value?.video_url)
    return

  uni.setClipboardData({
    data: videoData.value.video_url,
    success: () => {
      uni.showToast({
        title: '链接已复制',
        icon: 'success',
      })
    },
  })
}

// 页面加载时获取视频数据
onMounted(() => {
  // 获取页面参数
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1]
  const options = currentPage.options || {}

  if (options.data) {
    // 从URL参数中获取解析结果数据
    try {
      const parsedData = JSON.parse(decodeURIComponent(options.data))
      videoData.value = parsedData
    }
    catch (error) {
      console.error('解析数据失败:', error)
      uni.showToast({
        title: '数据解析失败',
        icon: 'none',
      })
      // 返回上一页
      setTimeout(() => {
        uni.navigateBack()
      }, 1500)
    }
  }
  else if (options.id) {
    // 这里应该根据ID从历史记录中获取数据
    uni.showToast({
      title: '功能开发中',
      icon: 'none',
    })
    setTimeout(() => {
      uni.navigateBack()
    }, 1500)
  }
  else {
    // 没有数据，返回上一页
    uni.showToast({
      title: '缺少数据',
      icon: 'none',
    })
    setTimeout(() => {
      uni.navigateBack()
    }, 1500)
  }
})
</script>

<template>
  <view class="min-h-screen bg-gray-50" :style="safeAreaStyle">
    <!-- 视频播放区域 -->
    <view class="relative mb-6 overflow-hidden rounded-2xl bg-black">
      <video
        v-if="videoData?.video_url"
        :src="videoData.video_url"
        :poster="videoData.cover"
        controls
        class="aspect-video w-full"
        object-fit="contain"
        show-center-play-btn
        show-play-btn
        enable-play-gesture
      />

      <!-- 加载状态 -->
      <view v-else class="aspect-video w-full flex items-center justify-center bg-gray-200">
        <text class="i-carbon-video text-4xl text-gray-400" />
      </view>
    </view>

    <!-- 视频信息 -->
    <view class="mb-6 rounded-2xl bg-white p-6 shadow-sm">
      <!-- 标题 -->
      <text class="line-clamp-3 mb-4 text-lg text-gray-800 font-semibold leading-relaxed">
        {{ videoData?.title }}
      </text>

      <!-- 作者信息 -->
      <view class="mb-4 flex items-center">
        <view class="mr-3 h-10 w-10 rounded-full bg-gradient-to-r from-pink-400 to-purple-500 flex items-center justify-center">
          <text class="i-carbon-user text-white text-lg" />
        </view>
        <view class="flex-1">
          <text class="text-base text-gray-800 font-medium">
            {{ videoData?.author }}
          </text>
        </view>
      </view>

      <!-- 统计信息 -->
      <view class="flex items-center justify-between pt-4 border-t border-gray-100">
        <!-- 点赞数 -->
        <view v-if="videoData?.like" class="flex items-center">
          <view class="mr-2 h-8 w-8 rounded-full bg-red-50 flex items-center justify-center">
            <text class="i-carbon-favorite text-red-500 text-sm" />
          </view>
          <view>
            <text class="text-sm text-gray-500">点赞</text>
            <text class="ml-1 text-base text-gray-800 font-semibold">
              {{ formatLikeCount(videoData.like) }}
            </text>
          </view>
        </view>

        <!-- 发布时间 -->
        <view v-if="videoData?.time" class="flex items-center">
          <view class="mr-2 h-8 w-8 rounded-full bg-blue-50 flex items-center justify-center">
            <text class="i-carbon-time text-blue-500 text-sm" />
          </view>
          <view>
            <text class="text-sm text-gray-500">发布</text>
            <text class="ml-1 text-base text-gray-800 font-medium">
              {{ formatPublishTime(videoData.time) }}
            </text>
          </view>
        </view>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="mb-6 rounded-2xl bg-white p-6 shadow-sm">
      <view class="grid grid-cols-2 gap-3">
        <button
          :disabled="!videoData?.video_url"
          class="flex items-center justify-center rounded-xl bg-blue-500 py-4 text-white active:bg-blue-600 disabled:bg-gray-300"
          @click="downloadVideo"
        >
          <text class="i-carbon-download mr-2" />
          <text>下载视频</text>
        </button>

        <button
          :disabled="!videoData?.video_url"
          class="flex items-center justify-center rounded-xl bg-green-500 py-4 text-white active:bg-green-600 disabled:bg-gray-300"
          @click="saveToAlbum"
        >
          <text class="i-carbon-save mr-2" />
          <text>保存相册</text>
        </button>
      </view>


    </view>



    <!-- 相关操作 -->
    <view class="mb-6 rounded-2xl bg-white p-6 shadow-sm">
      <text class="mb-4 text-lg text-gray-800 font-semibold">
        相关操作
      </text>

      <view class="space-y-3">
        <view
          class="flex items-center rounded-lg bg-gray-50 p-3 active:bg-gray-100"
          @click="copyVideoUrl"
        >
          <text class="i-carbon-copy mr-3 text-green-500" />
          <text class="text-sm text-gray-700">
            复制视频链接
          </text>
        </view>
      </view>
    </view>
  </view>
</template>

<style scoped>
.line-clamp-2 {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.space-x-4 > view:not(:first-child) {
  margin-left: 1rem;
}

.space-y-3 > view:not(:first-child) {
  margin-top: 0.75rem;
}

.grid {
  display: grid;
}

.grid-cols-2 {
  grid-template-columns: repeat(2, 1fr);
}

.gap-3 {
  gap: 0.75rem;
}

.aspect-video {
  aspect-ratio: 16 / 9;
}
</style>
