<route lang="jsonc">
{
  "style": {
    "navigationBarTitleText": "视频详情",
    "navigationBarBackgroundColor": "#FFFFFF",
    "navigationBarTextStyle": "black"
  }
}
</route>

<script setup lang="ts">
import { onMounted, ref } from 'vue'
import dayjs from 'dayjs'
import relativeTime from 'dayjs/plugin/relativeTime'
import 'dayjs/locale/zh-cn'
import { addDownloadRecord } from '@/utils/historyManager'
import { getSafeAreaStyle } from '@/utils/safeArea'

// 配置 dayjs
dayjs.extend(relativeTime)
dayjs.locale('zh-cn')

// 获取安全区域样式
const safeAreaStyle = getSafeAreaStyle()

// 响应式数据
const videoData = ref<any>(null)

// 下载视频
async function downloadVideo() {
  if (!videoData.value?.video_url)
    return

  try {
    // 下载成功后添加到历史记录
    if (videoData.value) {
      await addDownloadRecord({
        type: 'video',
        title: videoData.value.title,
        author: videoData.value.author,
        cover: videoData.value.cover,
        url: videoData.value.video_url,
        size: videoData.value.size,
        duration: videoData.value.duration,
        originalUrl: videoData.value.originalUrl,
      })
    }

    uni.showToast({
      title: '下载完成',
      icon: 'success',
    })
  }
  catch (error) {
    console.error('下载失败:', error)
    uni.showToast({
      title: '下载失败',
      icon: 'none',
    })
  }
}

// 保存到相册
async function saveToAlbum() {
  if (!videoData.value?.video_url)
    return

  try {
    // 保存成功后添加到历史记录
    if (videoData.value) {
      await addDownloadRecord({
        type: 'video',
        title: videoData.value.title,
        author: videoData.value.author,
        cover: videoData.value.cover,
        url: videoData.value.video_url,
        size: videoData.value.size,
        duration: videoData.value.duration,
        originalUrl: videoData.value.originalUrl,
      })
    }

    uni.showToast({
      title: '保存成功',
      icon: 'success',
    })
  }
  catch (error) {
    console.error('保存失败:', error)
    uni.showToast({
      title: '保存失败',
      icon: 'none',
    })
  }
}





// 复制视频链接
function copyVideoUrl() {
  if (!videoData.value?.video_url)
    return

  uni.setClipboardData({
    data: videoData.value.video_url,
    success: () => {
      uni.showToast({
        title: '链接已复制',
        icon: 'success',
      })
    },
  })
}

// 页面加载时获取视频数据
onMounted(() => {
  // 获取页面参数
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1]
  const options = currentPage.options || {}

  if (options.data) {
    // 从URL参数中获取解析结果数据
    try {
      const parsedData = JSON.parse(decodeURIComponent(options.data))
      videoData.value = parsedData
    }
    catch (error) {
      console.error('解析数据失败:', error)
      uni.showToast({
        title: '数据解析失败',
        icon: 'none',
      })
      // 返回上一页
      setTimeout(() => {
        uni.navigateBack()
      }, 1500)
    }
  }
  else if (options.id) {
    // 这里应该根据ID从历史记录中获取数据
    uni.showToast({
      title: '功能开发中',
      icon: 'none',
    })
    setTimeout(() => {
      uni.navigateBack()
    }, 1500)
  }
  else {
    // 没有数据，返回上一页
    uni.showToast({
      title: '缺少数据',
      icon: 'none',
    })
    setTimeout(() => {
      uni.navigateBack()
    }, 1500)
  }
})
</script>

<template>
  <view class="page-container" :style="safeAreaStyle">
    <!-- 视频播放区域 -->
    <view class="video-container">
      <view class="video-wrapper">
        <video
          v-if="videoData?.video_url"
          :src="videoData.video_url"
          :poster="videoData.cover"
          controls
          class="video-player"
          object-fit="cover"
          show-center-play-btn
          show-play-btn
          enable-play-gesture
        />

        <!-- 加载状态 -->
        <view v-else class="video-loading">
          <wd-loading type="circular" size="40px" />
          <text class="loading-text">视频加载中...</text>
        </view>

        <!-- 视频信息覆盖层 -->
        <view v-if="videoData?.video_url" class="video-overlay">
          <view class="overlay-content">
            <view class="stats-badges">
              <view v-if="videoData?.like" class="stat-badge like-badge">
                <wd-icon name="like-fill" size="12px" color="#ff4757" />
                <text class="badge-text">{{ videoData.like }}</text>
              </view>
              <view v-if="videoData?.time" class="stat-badge time-badge">
                <wd-icon name="time" size="12px" color="#5dade2" />
                <text class="badge-text">{{ dayjs.unix(videoData.time).format('MM-DD HH:mm') }}</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 视频信息 -->
    <wd-card class="info-card">
      <view class="video-info-content">
        <!-- 视频标题 -->
        <view class="title-section">
          <text class="video-title">{{ videoData?.title }}</text>
        </view>

        <!-- 作者信息 -->
        <view class="author-section">
          <view class="author-avatar">
            <wd-icon name="user" size="18px" color="#fff" />
          </view>
          <view class="author-info">
            <text class="author-name">{{ videoData?.author }}</text>
            <text class="author-label">创作者</text>
          </view>
        </view>

        <!-- 统计信息 -->
        <view v-if="videoData?.like || videoData?.time" class="stats-section">
          <view v-if="videoData?.like" class="stat-item like-stat">
            <wd-icon name="like-o" size="16px" color="#ff4757" />
            <text class="stat-text">{{ videoData.like }}</text>
          </view>

          <view v-if="videoData?.time" class="stat-item time-stat">
            <wd-icon name="time" size="16px" color="#5352ed" />
            <text class="stat-text">{{ dayjs.unix(videoData.time).format('YYYY-MM-DD HH:mm:ss') }}</text>
          </view>
        </view>
      </view>
    </wd-card>

    <!-- 操作按钮 -->
    <wd-card  class="action-card">
      <view class="action-buttons">
        <wd-button
          type="primary"
          size="large"
          :disabled="!videoData?.video_url"
          @click="downloadVideo"
          custom-style="margin-bottom: 12px;"
          block
        >
          <wd-icon name="download" size="16px" />
          下载视频
        </wd-button>

        <wd-button
          type="success"
          size="large"
          :disabled="!videoData?.video_url"
          @click="saveToAlbum"
          custom-style="margin-bottom: 12px;"
          block
        >
          <wd-icon name="picture" size="16px" />
          保存到相册
        </wd-button>

        <wd-button
          type="warning"
          size="large"
          :disabled="!videoData?.video_url"
          @click="copyVideoUrl"
          block
        >
          <wd-icon name="copy" size="16px" />
          复制视频链接
        </wd-button>
      </view>
    </wd-card>




  </view>
</template>

<style scoped>
.page-container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 16px;
}

.video-card {
  margin-bottom: 16px;
}

.video-player {
  width: 100%;
  aspect-ratio: 16 / 9;
  border-radius: 8px;
}

.video-loading {
  width: 100%;
  aspect-ratio: 16 / 9;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #f0f0f0;
  border-radius: 8px;
}

.loading-text {
  margin-top: 12px;
  color: #999;
  font-size: 14px;
}

.info-card {
  margin-bottom: 16px;
}

.video-info-content {
  padding: 8px 0;
}

.title-section {
  margin-bottom: 20px;
}

.video-title {
  font-size: 18px;
  font-weight: bold;
  line-height: 1.6;
  color: #2c3e50;
  word-break: break-all;
  letter-spacing: 0.5px;
}

.author-section {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  padding: 12px 0;
}

.author-avatar {
  width: 44px;
  height: 44px;
  border-radius: 22px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.author-info {
  flex: 1;
}

.author-name {
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
  display: block;
  margin-bottom: 4px;
}

.author-label {
  font-size: 12px;
  color: #7f8c8d;
  display: block;
}

.stats-section {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
  padding-top: 16px;
  border-top: 1px solid #ecf0f1;
}

.stat-item {
  display: flex;
  align-items: center;
  background: #f8f9fa;
  padding: 8px 12px;
  border-radius: 20px;
  gap: 6px;
}

.like-stat {
  background: linear-gradient(135deg, #ff6b6b 0%, #ff5252 100%);
}

.like-stat .stat-text {
  color: #fff;
  font-weight: 600;
}

.time-stat {
  background: linear-gradient(135deg, #4834d4 0%, #686de0 100%);
}

.time-stat .stat-text {
  color: #fff;
  font-weight: 500;
  font-size: 12px;
}

.stat-text {
  font-size: 14px;
  color: #2c3e50;
  font-weight: 500;
}

.action-card {
  margin-bottom: 16px;
}

.action-buttons {
  padding: 8px 0;
}
</style>
