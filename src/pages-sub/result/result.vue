<route lang="jsonc">
{
  "style": {
    "navigationBarTitleText": "视频详情",
    "navigationBarBackgroundColor": "#FFFFFF",
    "navigationBarTextStyle": "black"
  }
}
</route>

<script setup lang="ts">
import { onMounted, ref } from 'vue'
import dayjs from 'dayjs'
import relativeTime from 'dayjs/plugin/relativeTime'
import 'dayjs/locale/zh-cn'
import { addDownloadRecord } from '@/utils/historyManager'
import { getSafeAreaStyle } from '@/utils/safeArea'

// 配置 dayjs
dayjs.extend(relativeTime)
dayjs.locale('zh-cn')

// 获取安全区域样式
const safeAreaStyle = getSafeAreaStyle()

// 响应式数据
const videoData = ref<any>(null)

// 下载视频
async function downloadVideo() {
  if (!videoData.value?.video_url)
    return

  try {
    // 下载成功后添加到历史记录
    if (videoData.value) {
      await addDownloadRecord({
        type: 'video',
        title: videoData.value.title,
        author: videoData.value.author,
        cover: videoData.value.cover,
        url: videoData.value.video_url,
        size: videoData.value.size,
        duration: videoData.value.duration,
        originalUrl: videoData.value.originalUrl,
      })
    }

    uni.showToast({
      title: '下载完成',
      icon: 'success',
    })
  }
  catch (error) {
    console.error('下载失败:', error)
    uni.showToast({
      title: '下载失败',
      icon: 'none',
    })
  }
}

// 保存到相册
async function saveToAlbum() {
  if (!videoData.value?.video_url)
    return

  try {
    // 保存成功后添加到历史记录
    if (videoData.value) {
      await addDownloadRecord({
        type: 'video',
        title: videoData.value.title,
        author: videoData.value.author,
        cover: videoData.value.cover,
        url: videoData.value.video_url,
        size: videoData.value.size,
        duration: videoData.value.duration,
        originalUrl: videoData.value.originalUrl,
      })
    }

    uni.showToast({
      title: '保存成功',
      icon: 'success',
    })
  }
  catch (error) {
    console.error('保存失败:', error)
    uni.showToast({
      title: '保存失败',
      icon: 'none',
    })
  }
}





// 复制视频链接
function copyVideoUrl() {
  if (!videoData.value?.video_url)
    return

  uni.setClipboardData({
    data: videoData.value.video_url,
    success: () => {
      uni.showToast({
        title: '链接已复制',
        icon: 'success',
      })
    },
  })
}

// 页面加载时获取视频数据
onMounted(() => {
  // 获取页面参数
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1]
  const options = currentPage.options || {}

  if (options.data) {
    // 从URL参数中获取解析结果数据
    try {
      const parsedData = JSON.parse(decodeURIComponent(options.data))
      videoData.value = parsedData
    }
    catch (error) {
      console.error('解析数据失败:', error)
      uni.showToast({
        title: '数据解析失败',
        icon: 'none',
      })
      // 返回上一页
      setTimeout(() => {
        uni.navigateBack()
      }, 1500)
    }
  }
  else if (options.id) {
    // 这里应该根据ID从历史记录中获取数据
    uni.showToast({
      title: '功能开发中',
      icon: 'none',
    })
    setTimeout(() => {
      uni.navigateBack()
    }, 1500)
  }
  else {
    // 没有数据，返回上一页
    uni.showToast({
      title: '缺少数据',
      icon: 'none',
    })
    setTimeout(() => {
      uni.navigateBack()
    }, 1500)
  }
})
</script>

<template>
  <view class="page-container" :style="safeAreaStyle">
    <!-- 视频播放区域 -->
    <wd-card class="video-card">
      <video
        v-if="videoData?.video_url"
        :src="videoData.video_url"
        :poster="videoData.cover"
        controls
        class="video-player"
        object-fit="contain"
        show-center-play-btn
        show-play-btn
        enable-play-gesture
      />

      <!-- 加载状态 -->
      <view v-else class="video-loading">
        <wd-loading type="circular" />
        <text class="loading-text">视频加载中...</text>
      </view>
    </wd-card>

    <!-- 视频信息 -->
    <wd-card title="视频信息" class="info-card">
      <!-- 标题 -->
      <wd-cell-group>
        <wd-cell>
          <template #title>
            <view class="video-title">标题:{{ videoData?.title }}</view>
          </template>
        </wd-cell>

        <!-- 作者信息 -->
        <wd-cell :title="videoData?.author" label="创作者">
          <template #icon>
            <wd-icon name="user" size="20px" />
          </template>
        </wd-cell>

        <!-- 统计信息 -->
        <wd-cell v-if="videoData?.like || videoData?.time">
          <template #title>
            <view class="stats-container">
              <wd-tag v-if="videoData?.like" type="danger" plain>
                <wd-icon name="like" size="12px" />
                {{ videoData.like }} 点赞
              </wd-tag>
              <wd-tag v-if="videoData?.time" type="primary" plain>
                <wd-icon name="time" size="12px" />
                {{ dayjs.unix(videoData.time).fromNow() }}
              </wd-tag>
            </view>
          </template>
        </wd-cell>
      </wd-cell-group>
    </wd-card>

    <!-- 操作按钮 -->
    <wd-card  class="action-card">
      <view class="action-buttons">
        <wd-button
          type="primary"
          size="large"
          :disabled="!videoData?.video_url"
          @click="downloadVideo"
          custom-style="margin-bottom: 12px;"
          block
        >
          <wd-icon name="download" size="16px" />
          下载视频
        </wd-button>

        <wd-button
          type="success"
          size="large"
          :disabled="!videoData?.video_url"
          @click="saveToAlbum"
          custom-style="margin-bottom: 12px;"
          block
        >
          <wd-icon name="picture" size="16px" />
          保存到相册
        </wd-button>

        <wd-button
          type="warning"
          size="large"
          :disabled="!videoData?.video_url"
          @click="copyVideoUrl"
          block
        >
          <wd-icon name="copy" size="16px" />
          复制视频链接
        </wd-button>
      </view>
    </wd-card>




  </view>
</template>

<style scoped>
.page-container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 16px;
}

.video-card {
  margin-bottom: 16px;
}

.video-player {
  width: 100%;
  aspect-ratio: 16 / 9;
  border-radius: 8px;
}

.video-loading {
  width: 100%;
  aspect-ratio: 16 / 9;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #f0f0f0;
  border-radius: 8px;
}

.loading-text {
  margin-top: 12px;
  color: #999;
  font-size: 14px;
}

.info-card {
  margin-bottom: 16px;
}

.video-title {
  font-size: 16px;
  font-weight: bold;
  line-height: 1.5;
  color: #333;
  word-break: break-all;
}

.stats-container {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.action-card {
  margin-bottom: 16px;
}

.action-buttons {
  padding: 8px 0;
}
</style>
