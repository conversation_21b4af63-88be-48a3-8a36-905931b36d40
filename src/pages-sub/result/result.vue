<route lang="jsonc">
{
  "style": {
    "navigationBarTitleText": "视频详情",
    "navigationBarBackgroundColor": "#FFFFFF",
    "navigationBarTextStyle": "black"
  }
}
</route>

<script setup lang="ts">
import { onMounted, ref } from 'vue'
import { addDownloadRecord } from '@/utils/historyManager'
import { getSafeAreaStyle } from '@/utils/safeArea'

// 获取安全区域样式
const safeAreaStyle = getSafeAreaStyle()

// 响应式数据
const videoData = ref<any>(null)
const isDownloading = ref(false)
const isSaving = ref(false)
const downloadProgress = ref(0)



// 下载视频
async function downloadVideo() {
  if (!videoData.value?.url)
    return

  isDownloading.value = true
  downloadProgress.value = 0

  try {
    // 模拟下载进度
    const progressInterval = setInterval(async () => {
      downloadProgress.value += 10
      if (downloadProgress.value >= 100) {
        clearInterval(progressInterval)
        isDownloading.value = false

        // 下载成功后添加到历史记录
        if (videoData.value) {
          await addDownloadRecord({
            type: 'video',
            title: videoData.value.title,
            author: videoData.value.author,
            cover: videoData.value.cover,
            url: videoData.value.url,
            size: videoData.value.size,
            duration: videoData.value.duration,
            originalUrl: videoData.value.originalUrl,
          })
        }

        uni.showToast({
          title: '下载完成',
          icon: 'success',
        })
      }
    }, 300)
  }
  catch (error) {
    console.error('下载失败:', error)
    uni.showToast({
      title: '下载失败',
      icon: 'none',
    })
    isDownloading.value = false
  }
}

// 保存到相册
async function saveToAlbum() {
  if (!videoData.value?.url)
    return

  isSaving.value = true

  try {
    // 这里应该调用实际的保存到相册功能
    setTimeout(async () => {
      isSaving.value = false

      // 保存成功后添加到历史记录
      if (videoData.value) {
        await addDownloadRecord({
          type: 'video',
          title: videoData.value.title,
          author: videoData.value.author,
          cover: videoData.value.cover,
          url: videoData.value.url,
          size: videoData.value.size,
          duration: videoData.value.duration,
          originalUrl: videoData.value.originalUrl,
        })
      }

      uni.showToast({
        title: '保存成功',
        icon: 'success',
      })
    }, 2000)
  }
  catch (error) {
    console.error('保存失败:', error)
    uni.showToast({
      title: '保存失败',
      icon: 'none',
    })
    isSaving.value = false
  }
}





// 复制视频链接
function copyVideoUrl() {
  if (!videoData.value?.url)
    return

  uni.setClipboardData({
    data: videoData.value.url,
    success: () => {
      uni.showToast({
        title: '链接已复制',
        icon: 'success',
      })
    },
  })
}

// 页面加载时获取视频数据
onMounted(() => {
  // 获取页面参数
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1]
  const options = currentPage.options || {}

  if (options.data) {
    // 从URL参数中获取解析结果数据
    try {
      const parsedData = JSON.parse(decodeURIComponent(options.data))
      videoData.value = parsedData
    }
    catch (error) {
      console.error('解析数据失败:', error)
      uni.showToast({
        title: '数据解析失败',
        icon: 'none',
      })
      // 返回上一页
      setTimeout(() => {
        uni.navigateBack()
      }, 1500)
    }
  }
  else if (options.id) {
    // 这里应该根据ID从历史记录中获取数据
    uni.showToast({
      title: '功能开发中',
      icon: 'none',
    })
    setTimeout(() => {
      uni.navigateBack()
    }, 1500)
  }
  else {
    // 没有数据，返回上一页
    uni.showToast({
      title: '缺少数据',
      icon: 'none',
    })
    setTimeout(() => {
      uni.navigateBack()
    }, 1500)
  }
})
</script>

<template>
  <view class="min-h-screen bg-gray-50" :style="safeAreaStyle">
    <!-- 视频播放区域 -->
    <view class="relative mb-6 overflow-hidden rounded-2xl bg-black">
      <video
        v-if="videoData?.video_url || videoData?.url"
        :src="videoData.video_url || videoData.url"
        :poster="videoData.cover"
        controls
        class="aspect-video w-full"
        object-fit="contain"
        show-center-play-btn
        show-play-btn
        enable-play-gesture
      />

      <!-- 加载状态 -->
      <view v-else class="aspect-video w-full flex items-center justify-center bg-gray-200">
        <text class="i-carbon-video text-4xl text-gray-400" />
      </view>
    </view>

    <!-- 视频信息 -->
    <view class="mb-6 rounded-2xl bg-white p-6 shadow-sm">
      <text class="line-clamp-2 mb-2 text-lg text-gray-800 font-semibold">
        {{ videoData?.title || '视频标题' }}
      </text>
      <text class="mb-4 text-sm text-gray-500">
        {{ videoData?.author || '未知作者' }}
      </text>


    </view>

    <!-- 操作按钮 -->
    <view class="mb-6 rounded-2xl bg-white p-6 shadow-sm">
      <view class="grid grid-cols-2 gap-3">
        <button
          :disabled="isDownloading || !videoData?.url"
          class="flex items-center justify-center rounded-xl bg-blue-500 py-4 text-white active:bg-blue-600 disabled:bg-gray-300"
          @click="downloadVideo"
        >
          <text class="i-carbon-download mr-2" />
          <text v-if="isDownloading">
            下载中...
          </text>
          <text v-else>
            下载视频
          </text>
        </button>

        <button
          :disabled="isSaving || !videoData?.url"
          class="flex items-center justify-center rounded-xl bg-green-500 py-4 text-white active:bg-green-600 disabled:bg-gray-300"
          @click="saveToAlbum"
        >
          <text class="i-carbon-save mr-2" />
          <text v-if="isSaving">
            保存中...
          </text>
          <text v-else>
            保存相册
          </text>
        </button>
      </view>


    </view>

    <!-- 下载进度 -->
    <view v-if="downloadProgress > 0" class="mb-6 rounded-2xl bg-white p-6 shadow-sm">
      <view class="mb-2 flex justify-between text-sm text-gray-600">
        <text>下载进度</text>
        <text>{{ downloadProgress }}%</text>
      </view>
      <view class="h-2 w-full rounded-full bg-gray-200">
        <view
          class="h-2 rounded-full bg-blue-500 transition-all duration-300"
          :style="{ width: `${downloadProgress}%` }"
        />
      </view>
    </view>

    <!-- 相关操作 -->
    <view class="mb-6 rounded-2xl bg-white p-6 shadow-sm">
      <text class="mb-4 text-lg text-gray-800 font-semibold">
        相关操作
      </text>

      <view class="space-y-3">
        <view
          class="flex items-center rounded-lg bg-gray-50 p-3 active:bg-gray-100"
          @click="copyVideoUrl"
        >
          <text class="i-carbon-copy mr-3 text-green-500" />
          <text class="text-sm text-gray-700">
            复制视频链接
          </text>
        </view>
      </view>
    </view>
  </view>
</template>

<style scoped>
.line-clamp-2 {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.space-x-4 > view:not(:first-child) {
  margin-left: 1rem;
}

.space-y-3 > view:not(:first-child) {
  margin-top: 0.75rem;
}

.grid {
  display: grid;
}

.grid-cols-2 {
  grid-template-columns: repeat(2, 1fr);
}

.gap-3 {
  gap: 0.75rem;
}

.aspect-video {
  aspect-ratio: 16 / 9;
}
</style>
