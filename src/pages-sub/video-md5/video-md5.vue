<route lang="jsonc">
{
  "style": {
    "navigationBarTitleText": "文件MD5修改",
    "navigationBarBackgroundColor": "#FFFFFF",
    "navigationBarTextStyle": "black"
  }
}
</route>

<template>
  <view class="min-h-screen bg-gray-50" :style="safeAreaStyle">
    <!-- 工具说明 -->
    <view class="p-6 mb-6 bg-white shadow-sm rounded-2xl">
      <view class="flex items-center mb-4">
        <text class="i-carbon-fingerprint-recognition text-blue-500 text-xl mr-2"></text>
        <text class="text-lg font-semibold text-gray-800">MD5修改工具</text>
      </view>
      
      <text class="text-sm text-gray-600 leading-relaxed">
        通过修改文件的MD5值，可以让相同的文件在某些平台上被识别为不同的文件。常用于规避重复检测。
      </text>
    </view>
    
    <!-- 文件选择区域 -->
    <view class="p-6 mb-6 bg-white shadow-sm rounded-2xl">
      <view class="mb-4">
        <text class="text-lg font-semibold text-gray-800">选择文件</text>
        <text class="block mt-1 text-sm text-gray-500">支持视频、图片等多种格式</text>
      </view>
      
      <!-- 文件选择按钮 -->
      <view 
        v-if="!selectedFile"
        @click="selectFile"
        class="flex flex-col items-center justify-center py-12 border-2 border-dashed border-gray-300 rounded-xl bg-gray-50 active:bg-gray-100"
      >
        <text class="i-carbon-cloud-upload text-4xl text-gray-400 mb-4"></text>
        <text class="text-gray-600 mb-2">点击选择文件</text>
        <text class="text-sm text-gray-400">支持视频、图片等格式</text>
      </view>
      
      <!-- 已选择的文件信息 -->
      <view v-else class="space-y-4">
        <view class="flex items-center p-4 bg-blue-50 rounded-xl">
          <text class="i-carbon-document text-blue-500 text-2xl mr-3"></text>
          <view class="flex-1">
            <text class="text-sm font-medium text-gray-800 block">{{ selectedFile.name }}</text>
            <text class="text-xs text-gray-500">{{ formatFileSize(selectedFile.size) }}</text>
          </view>
          <button 
            @click="removeFile"
            class="p-2 text-gray-400 hover:text-red-500"
          >
            <text class="i-carbon-close"></text>
          </button>
        </view>
        
        <!-- 原始MD5 -->
        <view v-if="originalMD5" class="p-4 bg-gray-50 rounded-xl">
          <text class="text-sm font-medium text-gray-700 block mb-2">原始MD5值：</text>
          <text class="text-xs font-mono text-gray-600 break-all">{{ originalMD5 }}</text>
        </view>
      </view>
    </view>
    
    <!-- 修改选项 -->
    <view v-if="selectedFile" class="p-6 mb-6 bg-white shadow-sm rounded-2xl">
      <view class="mb-4">
        <text class="text-lg font-semibold text-gray-800">修改选项</text>
      </view>
      
      <view class="space-y-3">
        <view 
          v-for="option in modifyOptions" 
          :key="option.value"
          @click="selectedOption = option.value"
          class="flex items-center p-3 border rounded-lg cursor-pointer"
          :class="selectedOption === option.value ? 'border-blue-500 bg-blue-50' : 'border-gray-200'"
        >
          <view 
            class="w-4 h-4 rounded-full border-2 mr-3 flex items-center justify-center"
            :class="selectedOption === option.value ? 'border-blue-500' : 'border-gray-300'"
          >
            <view 
              v-if="selectedOption === option.value"
              class="w-2 h-2 bg-blue-500 rounded-full"
            ></view>
          </view>
          <view class="flex-1">
            <text class="text-sm font-medium text-gray-800 block">{{ option.label }}</text>
            <text class="text-xs text-gray-500">{{ option.desc }}</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 操作按钮 -->
    <view v-if="selectedFile" class="p-6 mb-6 bg-white shadow-sm rounded-2xl">
      <button 
        @click="startModify"
        :disabled="isProcessing || !selectedOption"
        class="w-full py-4 text-white bg-blue-500 rounded-xl disabled:bg-gray-300 active:bg-blue-600 mb-3"
      >
        <text v-if="isProcessing">处理中...</text>
        <text v-else>开始修改MD5</text>
      </button>
      
      <view v-if="processProgress > 0" class="mt-4">
        <view class="flex justify-between text-sm text-gray-600 mb-2">
          <text>处理进度</text>
          <text>{{ processProgress }}%</text>
        </view>
        <view class="w-full bg-gray-200 rounded-full h-2">
          <view 
            class="bg-blue-500 h-2 rounded-full transition-all duration-300"
            :style="{ width: processProgress + '%' }"
          ></view>
        </view>
      </view>
    </view>
    
    <!-- 处理结果 -->
    <view v-if="modifiedFile" class="p-6 mb-6 bg-white shadow-sm rounded-2xl">
      <view class="flex items-center mb-4">
        <text class="i-carbon-checkmark-filled text-green-500 text-xl mr-2"></text>
        <text class="text-lg font-semibold text-gray-800">处理完成</text>
      </view>
      
      <view class="space-y-4">
        <view class="p-4 bg-green-50 rounded-xl">
          <text class="text-sm font-medium text-gray-700 block mb-2">新MD5值：</text>
          <text class="text-xs font-mono text-gray-600 break-all">{{ modifiedFile.md5 }}</text>
        </view>
        
        <button 
          @click="downloadFile"
          class="w-full py-3 text-white bg-green-500 rounded-lg active:bg-green-600"
        >
          <text class="i-carbon-download mr-2"></text>
          <text>下载修改后的文件</text>
        </button>
      </view>
    </view>
    
    <!-- 注意事项 -->
    <view class="p-6 mb-20 bg-white shadow-sm rounded-2xl">
      <view class="flex items-center mb-4">
        <text class="i-carbon-warning text-orange-500 text-xl mr-2"></text>
        <text class="text-lg font-semibold text-gray-800">注意事项</text>
      </view>
      
      <view class="space-y-2 text-sm text-gray-600">
        <text class="block">• 修改MD5不会影响文件的实际内容和质量</text>
        <text class="block">• 处理大文件可能需要较长时间，请耐心等待</text>
        <text class="block">• 建议在WiFi环境下使用，避免消耗过多流量</text>
        <text class="block">• 请合理使用此功能，遵守相关平台规则</text>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { getSafeAreaStyle } from '@/utils/safeArea'

// 获取安全区域样式
const safeAreaStyle = getSafeAreaStyle()

// 响应式数据
const selectedFile = ref<any>(null)
const originalMD5 = ref('')
const selectedOption = ref('random')
const isProcessing = ref(false)
const processProgress = ref(0)
const modifiedFile = ref<any>(null)

// 修改选项
const modifyOptions = [
  {
    value: 'random',
    label: '随机修改',
    desc: '在文件末尾添加随机数据'
  },
  {
    value: 'timestamp',
    label: '时间戳修改',
    desc: '在文件末尾添加当前时间戳'
  },
  {
    value: 'custom',
    label: '自定义修改',
    desc: '添加自定义的标识数据'
  }
]

// 选择文件
const selectFile = () => {
  uni.chooseFile({
    count: 1,
    type: 'all',
    success: (res) => {
      if (res.tempFiles && res.tempFiles.length > 0) {
        const file = res.tempFiles[0]
        selectedFile.value = {
          name: file.name || '未知文件',
          size: file.size || 0,
          path: file.path
        }
        
        // 模拟计算原始MD5
        calculateOriginalMD5()
      }
    },
    fail: (err) => {
      console.error('选择文件失败:', err)
      uni.showToast({
        title: '选择文件失败',
        icon: 'none'
      })
    }
  })
}

// 移除文件
const removeFile = () => {
  selectedFile.value = null
  originalMD5.value = ''
  modifiedFile.value = null
  processProgress.value = 0
}

// 计算原始MD5（模拟）
const calculateOriginalMD5 = () => {
  // 这里应该调用实际的MD5计算函数
  // 现在用模拟数据
  setTimeout(() => {
    originalMD5.value = 'a1b2c3d4e5f6789012345678901234567890abcd'
  }, 1000)
}

// 开始修改MD5
const startModify = async () => {
  if (!selectedFile.value || !selectedOption.value) return
  
  isProcessing.value = true
  processProgress.value = 0
  
  try {
    // 模拟处理进度
    const progressInterval = setInterval(() => {
      processProgress.value += 10
      if (processProgress.value >= 100) {
        clearInterval(progressInterval)
        completeModification()
      }
    }, 200)
    
  } catch (error) {
    console.error('修改MD5失败:', error)
    uni.showToast({
      title: '修改失败',
      icon: 'none'
    })
    isProcessing.value = false
  }
}

// 完成修改
const completeModification = () => {
  isProcessing.value = false
  
  // 模拟生成新的MD5值
  modifiedFile.value = {
    name: selectedFile.value.name,
    size: selectedFile.value.size + 100, // 模拟增加的数据大小
    md5: 'b2c3d4e5f6789012345678901234567890abcdef',
    path: selectedFile.value.path + '_modified'
  }
  
  uni.showToast({
    title: '修改完成',
    icon: 'success'
  })
}

// 下载文件
const downloadFile = () => {
  if (!modifiedFile.value) return
  
  // 这里应该实现实际的文件下载逻辑
  uni.showToast({
    title: '功能开发中...',
    icon: 'none'
  })
}

// 格式化文件大小
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B'
  
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i]
}
</script>

<style scoped>
.space-y-4 > view:not(:first-child) {
  margin-top: 1rem;
}

.space-y-3 > view:not(:first-child) {
  margin-top: 0.75rem;
}

.space-y-2 > text:not(:first-child) {
  margin-top: 0.5rem;
}

.break-all {
  word-break: break-all;
}
</style>
