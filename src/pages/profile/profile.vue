<route lang="jsonc">
{
  "layout": "tabbar",
  "style": {
    "navigationBarTitleText": "我的",
    "navigationBarBackgroundColor": "#FFFFFF",
    "navigationBarTextStyle": "black"
  }
}
</route>

<template>
  <view class="min-h-screen px-4 pb-10 mx-auto bg-gray-50" :style="safeAreaStyle">
    <!-- 微型加载动画 -->
    <view v-if="isNavigating" class="mini-loading"></view>
    
    <!-- 用户资料卡片 -->
    <view class="p-5 mb-6 bg-white shadow-sm rounded-2xl">
      <view class="flex items-center">
        <view class="flex items-center justify-center w-16 h-16 mr-4 overflow-hidden bg-transparent rounded-full">
          <!-- 使用logo图片作为头像 -->
          <image src="/static/images/ppxlogo.png" mode="aspectFit" class="w-full h-full"></image>
        </view>
        <view class="flex-1">
          <text class="mb-1 text-lg font-medium text-gray-800">趣水印</text>
          <text class="text-sm text-gray-500">Version: 1.0.0</text>
        </view>
      </view>
    </view>
    
    <!-- 功能菜单 -->
    <view class="py-1 mb-6 bg-white shadow-sm rounded-2xl">
      <view class="flex items-center px-5 py-4 menu-item active:bg-gray-50" @click="navigateToHistory">
        <view class="flex items-center justify-center w-8 h-8 mr-3 bg-blue-100 rounded-full">
          <text class="text-blue-500 i-carbon-time"></text>
        </view>
        <text class="font-medium text-gray-800">下载记录</text>
        <text class="ml-auto text-gray-400 i-carbon-chevron-right"></text>
      </view>
    </view>
    
    <!-- 其他设置菜单 -->
    <view class="py-1 mb-6 bg-white shadow-sm rounded-2xl">
      <view class="flex items-center px-5 py-4 menu-item active:bg-gray-50" @click="navigateToHelp">
        <view class="flex items-center justify-center w-8 h-8 mr-3 bg-gray-100 rounded-full">
          <text class="text-gray-500 i-carbon-help"></text>
        </view>
        <text class="font-medium text-gray-800">帮助与反馈</text>
        <text class="ml-auto text-gray-400 i-carbon-chevron-right"></text>
      </view>
      
      <!-- 使用button+open-type="share"直接触发分享 -->
      <button open-type="share" class="share-button">
        <view class="flex items-center px-5 py-4 menu-item active:bg-gray-50">
          <view class="flex items-center justify-center w-8 h-8 mr-3 bg-gray-100 rounded-full">
            <text class="text-gray-500 i-carbon-share"></text>
          </view>
          <text class="font-medium text-gray-800">分享应用</text>
          <text class="ml-auto text-gray-400 i-carbon-chevron-right"></text>
        </view>
      </button>
    </view>
    
    <!-- 应用信息 -->
    <view class="mt-8 text-center">
      <text class="text-xs text-gray-400">趣水印 v1.0.0</text>
      <text class="block mt-1 text-xs text-gray-400">© 2025 迷失</text>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useUserStore } from '@/store/user'
import { getSafeAreaStyle } from '@/utils/safeArea'

// 获取安全区域样式
const safeAreaStyle = getSafeAreaStyle()

// 获取用户状态管理
const userStore = useUserStore()

// 定义加载状态变量
const isNavigating = ref(false)

// 显示微型加载动画并导航（带错误处理）
const navigateWithAnimation = (url: string) => {
  isNavigating.value = true

  setTimeout(() => {
    isNavigating.value = false
    uni.navigateTo({
      url,
      animationType: 'slide-in-right',
      animationDuration: 300,
      success: () => {
        console.log('跳转成功:', url)
      },
      fail: (err) => {
        console.error('跳转失败:', err)
        uni.showToast({
          title: '页面跳转失败',
          icon: 'none'
        })
      }
    })
  }, 300)
}

// 跳转到历史记录（分包路径）
const navigateToHistory = () => {
  navigateWithAnimation('/pages-sub/history/history')
}

// 帮助与反馈（分包路径）
const navigateToHelp = () => {
  navigateWithAnimation('/pages-sub/help/help')
}

// 处理分享 - 暴露给页面
// #ifdef MP-WEIXIN
const onShareAppMessage = () => {
  return {
    title: '趣水印 - 一键去除短视频水印',
    path: '/pages/index/index',
    imageUrl: '/static/images/ppxlogo.png'
  }
}

// 同样支持分享到朋友圈
const onShareTimeline = () => {
  return {
    title: '趣水印 - 一键去除短视频水印',
    path: '/pages/index/index',
    imageUrl: '/static/images/ppxlogo.png'
  }
}

defineExpose({
  onShareAppMessage,
  onShareTimeline
})
// #endif

// 页面加载时确保分享功能已启用
onMounted(() => {
  // #ifdef MP-WEIXIN
  if (wx.showShareMenu) {
    wx.showShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage', 'shareTimeline']
    })
  }
  // #endif
})
</script>

<style scoped>
/* 添加微型加载指示器的样式 */
.mini-loading {
  width: 24px;
  height: 24px;
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  border: 2px solid rgba(59, 130, 246, 0.2);
  border-top-color: #3B82F6;
  border-radius: 50%;
  animation: spin 0.8s linear infinite;
  z-index: 9999;
}

@keyframes spin {
  from { transform: translate(-50%, -50%) rotate(0deg); }
  to { transform: translate(-50%, -50%) rotate(360deg); }
}

.menu-item {
  position: relative;
}

.menu-item::after {
  content: '';
  position: absolute;
  left: 16px;
  right: 16px;
  bottom: 0;
  height: 1px;
  background-color: #f3f4f6;
}

.menu-item:last-child::after {
  display: none;
}

/* 分享按钮样式重置 */
.share-button {
  background: none;
  margin: 0;
  padding: 0;
  width: 100%;
  text-align: left;
  line-height: normal;
  border-radius: 0;
  border: none;
  font-size: inherit;
  color: inherit;
}

.share-button::after {
  border: none;
}
</style>
