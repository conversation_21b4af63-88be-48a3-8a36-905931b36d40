import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { HistoryItem, HistoryFilter, AddHistoryParams, DateGroup } from '@/types/history'

/**
 * 历史记录状态管理
 */
export const useHistoryStore = defineStore(
  'history',
  () => {
    // 状态定义
    const historyList = ref<HistoryItem[]>([])
    const currentFilter = ref<HistoryFilter>('all')
    const loading = ref(false)
    const lastUpdated = ref(Date.now())

    // 计算属性 - 根据筛选条件过滤历史记录
    const filteredHistory = computed(() => {
      if (currentFilter.value === 'all') {
        return historyList.value
      }
      return historyList.value.filter(item => item.type === currentFilter.value)
    })

    // 计算属性 - 按日期分组
    const groupedHistory = computed(() => {
      const today: HistoryItem[] = []
      const yesterday: HistoryItem[] = []
      const earlier: HistoryItem[] = []

      filteredHistory.value.forEach(item => {
        switch (item.dateGroup) {
          case 'today':
            today.push(item)
            break
          case 'yesterday':
            yesterday.push(item)
            break
          case 'earlier':
            earlier.push(item)
            break
        }
      })

      return { today, yesterday, earlier }
    })

    // 计算属性 - 最近的几条记录（用于首页显示）
    const recentHistory = computed(() => {
      return historyList.value.slice(0, 5)
    })

    // 工具函数 - 生成唯一ID
    const generateId = (): string => {
      return Date.now().toString(36) + Math.random().toString(36).substr(2)
    }

    // 工具函数 - 获取日期分组
    const getDateGroup = (timestamp: number): DateGroup => {
      const now = new Date()
      const targetDate = new Date(timestamp)
      const yesterday = new Date(now)
      yesterday.setDate(yesterday.getDate() - 1)

      if (targetDate.toDateString() === now.toDateString()) {
        return 'today'
      } else if (targetDate.toDateString() === yesterday.toDateString()) {
        return 'yesterday'
      } else {
        return 'earlier'
      }
    }

    // 工具函数 - 格式化时间显示
    const formatTime = (timestamp: number): string => {
      const date = new Date(timestamp)
      const now = new Date()
      const diffDays = Math.floor((now.getTime() - timestamp) / (24 * 60 * 60 * 1000))

      const hours = date.getHours().toString().padStart(2, '0')
      const minutes = date.getMinutes().toString().padStart(2, '0')
      const timeStr = `${hours}:${minutes}`

      if (diffDays === 0) {
        return `今天 ${timeStr}`
      } else if (diffDays === 1) {
        return `昨天 ${timeStr}`
      } else if (diffDays < 7) {
        const days = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']
        const dayOfWeek = days[date.getDay()]
        return `${dayOfWeek} ${timeStr}`
      } else {
        const month = (date.getMonth() + 1).toString().padStart(2, '0')
        const day = date.getDate().toString().padStart(2, '0')
        return `${month}-${day} ${timeStr}`
      }
    }

    // 添加历史记录
    const addHistory = (params: AddHistoryParams): void => {
      const timestamp = Date.now()
      const newItem: HistoryItem = {
        id: generateId(),
        timestamp,
        createdAt: timestamp,
        updatedAt: timestamp,
        time: formatTime(timestamp),
        dateGroup: getDateGroup(timestamp),
        ...params
      }

      // 检查是否已存在相同的记录（基于originalUrl）
      if (params.originalUrl) {
        const existingIndex = historyList.value.findIndex(
          item => item.originalUrl === params.originalUrl
        )
        if (existingIndex !== -1) {
          // 更新现有记录
          historyList.value[existingIndex] = {
            ...historyList.value[existingIndex],
            ...newItem,
            id: historyList.value[existingIndex].id, // 保持原有ID
            createdAt: historyList.value[existingIndex].createdAt // 保持原有创建时间
          }
          // 移动到列表顶部
          const updatedItem = historyList.value.splice(existingIndex, 1)[0]
          historyList.value.unshift(updatedItem)
          return
        }
      }

      // 添加新记录到列表顶部
      historyList.value.unshift(newItem)
      
      // 限制历史记录数量（最多保存100条）
      if (historyList.value.length > 100) {
        historyList.value = historyList.value.slice(0, 100)
      }

      lastUpdated.value = Date.now()
    }

    // 删除单条历史记录
    const removeHistory = (id: string): void => {
      const index = historyList.value.findIndex(item => item.id === id)
      if (index !== -1) {
        historyList.value.splice(index, 1)
        lastUpdated.value = Date.now()
      }
    }

    // 清空所有历史记录
    const clearHistory = (): void => {
      historyList.value = []
      lastUpdated.value = Date.now()
    }

    // 设置筛选条件
    const setFilter = (filter: HistoryFilter): void => {
      currentFilter.value = filter
    }

    // 从本地存储加载历史记录
    const loadHistory = (): void => {
      loading.value = true
      try {
        const stored = uni.getStorageSync('downloadHistory')
        if (stored) {
          const parsedHistory = JSON.parse(stored)
          if (Array.isArray(parsedHistory)) {
            // 重新处理时间格式和分组
            historyList.value = parsedHistory.map(item => ({
              ...item,
              time: formatTime(item.timestamp),
              dateGroup: getDateGroup(item.timestamp)
            }))
          }
        }
      } catch (error) {
        console.error('加载历史记录失败:', error)
        historyList.value = []
      } finally {
        loading.value = false
      }
    }

    // 保存历史记录到本地存储
    const saveHistory = (): void => {
      try {
        uni.setStorageSync('downloadHistory', JSON.stringify(historyList.value))
      } catch (error) {
        console.error('保存历史记录失败:', error)
      }
    }

    // 监听历史记录变化，自动保存
    const saveHistoryDebounced = (() => {
      let timer: NodeJS.Timeout | null = null
      return () => {
        if (timer) clearTimeout(timer)
        timer = setTimeout(saveHistory, 500)
      }
    })()

    // 返回状态和方法
    return {
      // 状态
      historyList,
      currentFilter,
      loading,
      lastUpdated,
      
      // 计算属性
      filteredHistory,
      groupedHistory,
      recentHistory,
      
      // 方法
      addHistory,
      removeHistory,
      clearHistory,
      setFilter,
      loadHistory,
      saveHistory,
      saveHistoryDebounced
    }
  },
  {
    // 启用持久化存储
    persist: {
      key: 'history-store',
      storage: {
        getItem: (key: string) => uni.getStorageSync(key),
        setItem: (key: string, value: string) => uni.setStorageSync(key, value),
        removeItem: (key: string) => uni.removeStorageSync(key)
      },
      paths: ['historyList', 'currentFilter', 'lastUpdated']
    }
  }
)
