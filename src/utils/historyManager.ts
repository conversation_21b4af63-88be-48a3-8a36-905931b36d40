/**
 * 历史记录管理插件
 * 按照Unibest项目规范组织的工具函数
 */

import type { HistoryItem, AddHistoryParams } from '@/types/history'

/**
 * 历史记录管理器类
 */
class HistoryManager {
  private readonly STORAGE_KEY = 'downloadHistory'
  private readonly MAX_RECORDS = 100

  /**
   * 添加历史记录
   * @param params 历史记录参数
   */
  async addRecord(params: AddHistoryParams): Promise<void> {
    try {
      const timestamp = Date.now()
      const newItem: HistoryItem = {
        id: this.generateId(),
        timestamp,
        createdAt: timestamp,
        updatedAt: timestamp,
        time: this.formatTime(timestamp),
        dateGroup: this.getDateGroup(timestamp),
        ...params
      }

      const existingRecords = await this.getRecords()
      
      // 检查是否已存在相同的记录（基于originalUrl）
      if (params.originalUrl) {
        const existingIndex = existingRecords.findIndex(
          item => item.originalUrl === params.originalUrl
        )
        if (existingIndex !== -1) {
          // 更新现有记录
          existingRecords[existingIndex] = {
            ...existingRecords[existingIndex],
            ...newItem,
            id: existingRecords[existingIndex].id, // 保持原有ID
            createdAt: existingRecords[existingIndex].createdAt // 保持原有创建时间
          }
          // 移动到列表顶部
          const updatedItem = existingRecords.splice(existingIndex, 1)[0]
          existingRecords.unshift(updatedItem)
          await this.saveRecords(existingRecords)
          return
        }
      }

      // 添加新记录到列表顶部
      existingRecords.unshift(newItem)
      
      // 限制历史记录数量
      if (existingRecords.length > this.MAX_RECORDS) {
        existingRecords.splice(this.MAX_RECORDS)
      }

      await this.saveRecords(existingRecords)
      
      console.log('历史记录添加成功:', newItem.title)
    } catch (error) {
      console.error('添加历史记录失败:', error)
      throw error
    }
  }

  /**
   * 获取所有历史记录
   */
  async getRecords(): Promise<HistoryItem[]> {
    try {
      const stored = uni.getStorageSync(this.STORAGE_KEY)
      if (!stored) return []
      
      const parsedHistory = JSON.parse(stored)
      if (!Array.isArray(parsedHistory)) return []
      
      // 重新处理时间格式和分组
      return parsedHistory.map(item => ({
        ...item,
        time: this.formatTime(item.timestamp),
        dateGroup: this.getDateGroup(item.timestamp)
      }))
    } catch (error) {
      console.error('获取历史记录失败:', error)
      return []
    }
  }

  /**
   * 删除单条历史记录
   * @param id 记录ID
   */
  async removeRecord(id: string): Promise<void> {
    try {
      const records = await this.getRecords()
      const filteredRecords = records.filter(item => item.id !== id)
      await this.saveRecords(filteredRecords)
      console.log('历史记录删除成功:', id)
    } catch (error) {
      console.error('删除历史记录失败:', error)
      throw error
    }
  }

  /**
   * 清空所有历史记录
   */
  async clearRecords(): Promise<void> {
    try {
      uni.removeStorageSync(this.STORAGE_KEY)
      console.log('历史记录已清空')
    } catch (error) {
      console.error('清空历史记录失败:', error)
      throw error
    }
  }

  /**
   * 根据类型筛选历史记录
   * @param type 内容类型
   */
  async getRecordsByType(type: 'all' | 'video' | 'img'): Promise<HistoryItem[]> {
    const records = await this.getRecords()
    if (type === 'all') return records
    return records.filter(item => item.type === type)
  }

  /**
   * 获取最近的几条记录
   * @param limit 数量限制
   */
  async getRecentRecords(limit: number = 5): Promise<HistoryItem[]> {
    const records = await this.getRecords()
    return records.slice(0, limit)
  }

  /**
   * 按日期分组获取记录
   */
  async getGroupedRecords(): Promise<{
    today: HistoryItem[]
    yesterday: HistoryItem[]
    earlier: HistoryItem[]
  }> {
    const records = await this.getRecords()
    const today: HistoryItem[] = []
    const yesterday: HistoryItem[] = []
    const earlier: HistoryItem[] = []

    records.forEach(item => {
      switch (item.dateGroup) {
        case 'today':
          today.push(item)
          break
        case 'yesterday':
          yesterday.push(item)
          break
        case 'earlier':
          earlier.push(item)
          break
      }
    })

    return { today, yesterday, earlier }
  }

  /**
   * 保存记录到本地存储
   * @private
   */
  private async saveRecords(records: HistoryItem[]): Promise<void> {
    try {
      uni.setStorageSync(this.STORAGE_KEY, JSON.stringify(records))
    } catch (error) {
      console.error('保存历史记录失败:', error)
      throw error
    }
  }

  /**
   * 生成唯一ID
   * @private
   */
  private generateId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substring(2)
  }

  /**
   * 获取日期分组
   * @private
   */
  private getDateGroup(timestamp: number): 'today' | 'yesterday' | 'earlier' {
    const now = new Date()
    const targetDate = new Date(timestamp)
    const yesterday = new Date(now)
    yesterday.setDate(yesterday.getDate() - 1)

    if (targetDate.toDateString() === now.toDateString()) {
      return 'today'
    } else if (targetDate.toDateString() === yesterday.toDateString()) {
      return 'yesterday'
    } else {
      return 'earlier'
    }
  }

  /**
   * 格式化时间显示
   * @private
   */
  private formatTime(timestamp: number): string {
    const date = new Date(timestamp)
    const now = new Date()
    const diffDays = Math.floor((now.getTime() - timestamp) / (24 * 60 * 60 * 1000))

    const hours = date.getHours().toString().padStart(2, '0')
    const minutes = date.getMinutes().toString().padStart(2, '0')
    const timeStr = `${hours}:${minutes}`

    if (diffDays === 0) {
      return `今天 ${timeStr}`
    } else if (diffDays === 1) {
      return `昨天 ${timeStr}`
    } else if (diffDays < 7) {
      const days = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']
      const dayOfWeek = days[date.getDay()]
      return `${dayOfWeek} ${timeStr}`
    } else {
      const month = (date.getMonth() + 1).toString().padStart(2, '0')
      const day = date.getDate().toString().padStart(2, '0')
      return `${month}-${day} ${timeStr}`
    }
  }
}

// 创建单例实例
const historyManager = new HistoryManager()

// 导出便捷函数
export const addDownloadRecord = historyManager.addRecord.bind(historyManager)
export const getDownloadRecords = historyManager.getRecords.bind(historyManager)
export const removeDownloadRecord = historyManager.removeRecord.bind(historyManager)
export const clearDownloadRecords = historyManager.clearRecords.bind(historyManager)
export const getRecordsByType = historyManager.getRecordsByType.bind(historyManager)
export const getRecentRecords = historyManager.getRecentRecords.bind(historyManager)
export const getGroupedRecords = historyManager.getGroupedRecords.bind(historyManager)

// 导出类实例（用于高级用法）
export { historyManager }

// 默认导出
export default historyManager
