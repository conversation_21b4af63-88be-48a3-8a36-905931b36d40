/**
 * 安全区域适配工具函数
 */

/**
 * 安全区域边距接口
 */
export interface SafeAreaInsets {
  top: number
  right: number
  bottom: number
  left: number
}

/**
 * 获取安全区域边距
 * @returns 安全区域边距对象
 */
export function getSafeAreaInsets(): SafeAreaInsets | null {
  let safeAreaInsets: SafeAreaInsets | null = null
  let systemInfo: any

  try {
    // #ifdef MP-WEIXIN
    // 微信小程序使用新的API
    systemInfo = uni.getWindowInfo()
    safeAreaInsets = systemInfo.safeArea
      ? {
          top: systemInfo.safeArea.top,
          right: systemInfo.windowWidth - systemInfo.safeArea.right,
          bottom: systemInfo.windowHeight - systemInfo.safeArea.bottom,
          left: systemInfo.safeArea.left,
        }
      : null
    // #endif

    // #ifndef MP-WEIXIN
    // 其他平台继续使用uni API
    systemInfo = uni.getSystemInfoSync()
    safeAreaInsets = systemInfo.safeAreaInsets
    // #endif
  } catch (error) {
    console.error('获取安全区域信息失败:', error)
    safeAreaInsets = null
  }

  return safeAreaInsets
}

/**
 * 获取状态栏高度
 * @returns 状态栏高度（px）
 */
export function getStatusBarHeight(): number {
  const safeAreaInsets = getSafeAreaInsets()
  return safeAreaInsets?.top || 0
}

/**
 * 获取底部安全区域高度
 * @returns 底部安全区域高度（px）
 */
export function getBottomSafeAreaHeight(): number {
  const safeAreaInsets = getSafeAreaInsets()
  return safeAreaInsets?.bottom || 0
}

/**
 * 获取带安全区域的顶部内边距
 * @param additionalPadding 额外的内边距（rpx）
 * @returns 顶部内边距（rpx）
 */
export function getSafeAreaTopPadding(additionalPadding: number = 0): number {
  const statusBarHeight = getStatusBarHeight()
  // 状态栏高度直接使用，不需要转换
  return statusBarHeight + additionalPadding
}

/**
 * 获取带安全区域的底部内边距
 * @param additionalPadding 额外的内边距（rpx）
 * @returns 底部内边距（rpx）
 */
export function getSafeAreaBottomPadding(additionalPadding: number = 24): number {
  const bottomSafeAreaHeight = getBottomSafeAreaHeight()
  // 状态栏高度直接使用，不需要转换
  return bottomSafeAreaHeight + additionalPadding
}

/**
 * 获取安全区域样式对象
 * @param options 配置选项
 * @returns 样式对象
 */
export function getSafeAreaStyle(options: {
  top?: number
  bottom?: number
  includeTop?: boolean
  includeBottom?: boolean
} = {}) {
  const {
    top = 0,
    bottom = 24,
    includeTop = true,
    includeBottom = false
  } = options

  const style: Record<string, string> = {}

  if (includeTop) {
    // 简化处理：只使用很小的固定rpx值，确保内容不被遮挡但间距不会太大
    style.paddingTop = `${top || 8}rpx`
  }

  if (includeBottom) {
    style.paddingBottom = `${getSafeAreaBottomPadding(bottom)}rpx`
  }

  return style
}
